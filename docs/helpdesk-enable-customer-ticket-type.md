# Enabling Customer Ticket Type Selection in Helpdesk App

## 📋 Issue Description

By default, the Frappe Helpdesk app restricts normal users (customers) to only creating ticket titles and descriptions when submitting support tickets. The **Ticket Type** and **Priority** fields are hidden from customers and can only be set by users with Agent roles.

### Current Behavior:
- ✅ Customers can set: Subject, Description
- ❌ Customers cannot set: Ticket Type, Priority, Agent Group
- ✅ Agents can set: All fields including Ticket Type and Priority

### Desired Behavior:
- ✅ Customers can set: Subject, Description, **Ticket Type**
- ❌ Customers still cannot set: Priority, Agent Group (business logic)
- ✅ Agents can set: All fields (unchanged)

## 🔍 Root Cause Analysis

The restriction exists due to multiple layers of access control in the helpdesk system:

### 1. **Template-Based Field Visibility**
The helpdesk app uses **HD Ticket Template** to control which fields are visible to customers vs agents. Each field in the template has a `hide_from_customer` flag that determines visibility.

### 2. **Business Logic Design**
The original design philosophy was:
- **Customers**: Provide problem description (what happened)
- **Agents**: Categorize and prioritize (how to handle it)

### 3. **Role-Based Access Control**
The system distinguishes between:
- **Customer Portal**: Limited field access for end users
- **Agent Portal**: Full field access for support staff

### 4. **Frontend Implementation**
The Vue.js frontend filters fields based on:
```javascript
// Only show fields that are not hidden from customers
.filter((f) => route.meta.agent || !f.hide_from_customer)
```

## 💡 Why Enable Customer Ticket Type Selection?

### Business Benefits:
1. **Better Categorization**: Customers know their issue type better than agents initially
2. **Faster Triage**: Pre-categorized tickets can be routed more efficiently
3. **Improved User Experience**: Customers feel more in control of their support request
4. **Reduced Agent Workload**: Less manual categorization needed

### Technical Benefits:
1. **Automated Routing**: Tickets can be auto-assigned based on type
2. **Better Reporting**: More accurate categorization from the source
3. **SLA Compliance**: Proper categorization enables correct SLA application

## 🛠️ Solution Implementation

### Method 1: Through Frappe UI (Recommended)

This method uses the existing template system to enable ticket type selection for customers.

#### Step-by-Step Instructions:

1. **Navigate to HD Ticket Template**
   ```
   Helpdesk > Setup > HD Ticket Template
   ```

2. **Open the Default Template**
   - Click on the "Default" template (or create a new one if needed)
   - This template controls which fields are shown to customers

3. **Add Ticket Type Field**
   In the **Fields** table, add a new row with the following configuration:

   | Field | Value | Description |
   |-------|-------|-------------|
   | **Field (fieldname)** | `ticket_type` | The internal field name that links to HD Ticket doctype |
   | **Required** | ☐ or ☑️ | Check if you want to make ticket type mandatory for customers |
   | **Hide from customer** | ☐ **UNCHECKED** | **This is the key setting!** Unchecking allows customers to see this field |
   | **URL/Method** | *(leave empty)* | Not needed for Link fields |
   | **Placeholder** | `Select ticket type` | Optional helpful text for customers |

4. **Save the Template**
   - Click **Save** to apply the changes
   - The changes take effect immediately

#### Configuration Example:
```
┌─────────────────────────────────────────────────────────────┐
│ HD Ticket Template: Default                                 │
├─────────────────────────────────────────────────────────────┤
│ Fields Table:                                               │
│ ┌─────────────┬──────────┬──────────────────┬─────────────┐ │
│ │ Field       │ Required │ Hide from        │ Placeholder │ │
│ │ (fieldname) │          │ Customer         │             │ │
│ ├─────────────┼──────────┼──────────────────┼─────────────┤ │
│ │ ticket_type │    ☐     │        ☐         │ Select...   │ │
│ └─────────────┴──────────┴──────────────────┴─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## ✅ Verification Steps

After implementing the solution, verify it works correctly:

### 1. **Customer Portal Test**
1. Log in as a customer (non-agent user)
2. Navigate to create a new ticket
3. Verify that "Ticket Type" dropdown appears
4. Confirm available options (Question, Bug, Incident, etc.)
5. Create a test ticket with a selected type

### 2. **Agent Portal Test**
1. Log in as an agent
2. View the ticket created by the customer
3. Confirm the ticket type was properly saved
4. Verify agents can still modify all fields

### 3. **Template Flexibility Test**
1. Create additional templates with different field configurations
2. Test that different customer groups can have different field access
3. Verify backward compatibility with existing tickets

## 🔧 Advanced Configuration Options

### Making Ticket Type Mandatory
If you want to require customers to select a ticket type:
1. In the template field configuration, check **Required**
2. Customers will be unable to submit tickets without selecting a type

### Creating Custom Templates
For different customer segments:
1. Create new HD Ticket Templates
2. Configure different field visibility per template
3. Assign templates based on customer type or portal

### Setting Default Values
To pre-select a ticket type:
1. Go to **Helpdesk > Setup > HD Settings**
2. Set **Default Ticket Type**
3. This will be pre-selected for new tickets

## 🚨 Important Considerations

### Security & Permissions
- ✅ Customers can only select from existing ticket types
- ✅ Customers cannot create new ticket types
- ✅ Agent-only fields (Priority, Assignment) remain protected

### Data Integrity
- ✅ All ticket type selections are validated against HD Ticket Type doctype
- ✅ Invalid selections are automatically rejected
- ✅ Existing tickets remain unaffected

### Performance Impact
- ✅ Minimal performance impact (uses existing template system)
- ✅ No additional database queries required
- ✅ Frontend caching remains effective

## 📊 Expected Results

After implementation, you should see:

### Customer Experience:
- Ticket creation form includes "Ticket Type" dropdown
- Available options: Question, Bug, Incident, etc.
- Optional or required based on your configuration
- Improved user experience and ticket categorization

### Agent Experience:
- Tickets arrive pre-categorized by customers
- Less manual categorization work required
- Better initial routing and SLA application
- All existing agent capabilities remain unchanged

### System Benefits:
- More accurate ticket categorization
- Better reporting and analytics
- Improved automated workflows
- Enhanced customer satisfaction

---

## 📝 Summary

This solution leverages the existing HD Ticket Template system to enable customer ticket type selection without compromising security or agent functionality. The key insight is that the `hide_from_customer` flag in the template controls field visibility, and unchecking it for the `ticket_type` field allows customers to participate in the categorization process while maintaining all existing access controls and business logic.
