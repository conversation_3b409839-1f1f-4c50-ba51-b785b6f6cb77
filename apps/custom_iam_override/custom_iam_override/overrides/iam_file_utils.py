import frappe

from frappe import generate_hash


def custom_set_file_details(file):
    print("new bl7777777777777")
    print("new bl7777777777777")
    print("new bl7777777777777")
    print("new bl7777777777777")
    """
    Custom implementation of set_file_details function that overrides the IAM version.
    This function provides enhanced validation and error handling.
    """
    try:
        from iam.utils.file import create_folders, get_file_sort_order
        # Sets the folder and file name of the uploaded file through admin panel
        if not (file.is_folder or file.from_api or file.get("is_details_set")):
            if file.attached_to_doctype and file.attached_to_name:
                # Ensure attached_to_doctype and attached_to_name are strings
                file.attached_to_doctype = str(file.attached_to_doctype)
                file.attached_to_name = str(file.attached_to_name)
                if not file.get("name_hash"):
                    file.name_hash = generate_hash(length=5)
                file_extension = file.file_name.split(".")[-1]
                file_name = f"{file.attached_to_doctype}-{file.attached_to_name}-{file.name_hash}.{file_extension}"
                file_name = file_name.replace(" ", "-").replace("_", "-").lower()

                file.folder = create_folders([file.attached_to_doctype, file.attached_to_name])
                file.file_name = file_name
                file.sort_order = get_file_sort_order(file.attached_to_doctype, file.attached_to_name)
                file.is_details_set = True
            else:
                file.file_name = file.file_name.replace(" ", "-").replace("_", "-").lower()
                file.is_details_set = True
    except ImportError as e:
        frappe.logger().warning(f"Could not import IAM module for overrides: {str(e)}")
    except Exception as e:
        frappe.logger().error(f"Error applying IAM overrides: {str(e)}")

def custom_write_file(file):
    """Custom implementation of write_file function"""
    custom_set_file_details(file)
    file.save_file_on_filesystem()


def custom_file_before_insert(file, method=None):
    """Custom implementation of file_before_insert hook"""
    # method parameter is required by Frappe hooks but not used here
    custom_set_file_details(file)

