# Custom IAM Override

A Frappe app that overrides the `set_file_details` function from the IAM app without modifying the original IAM code.

## 🎯 Purpose

This app provides a clean way to override IAM file handling functions using monkey patching, allowing you to:
- Fix bugs in IAM file handling without modifying the original code
- Add custom validation and error handling
- Maintain separation between your customizations and the original IAM app
- Easily update or rollback changes

## 🔧 What It Overrides

### Functions Overridden:
- `iam.utils.file.set_file_details()`
- `iam.utils.file.write_file()`
- `iam.utils.file.file_before_insert()`

### Key Improvements:
- ✅ Enhanced type validation for `attached_to_doctype` and `attached_to_name`
- ✅ Better error handling with meaningful error messages
- ✅ Safe string operations with null checks
- ✅ Maintains all original functionality while fixing TypeError issues

## 📦 Installation

```bash
# Install the app
bench --site your-site-name install-app custom_iam_override

# Restart bench
bench restart
```

## 🔍 Verification

```python
# In frappe console (bench console)
import iam.utils.file as iam_file_utils
print(hasattr(iam_file_utils, '_original_set_file_details'))
# Should return True if override is active
```

#### License

MIT