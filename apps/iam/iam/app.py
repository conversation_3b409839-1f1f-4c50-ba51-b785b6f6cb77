import frappe, iam, json
import frappe.app
from werkzeug.exceptions import MethodNotAllowed
from frappe.utils.response import json_handler
from iam.utils import is_enabled_iam_settings, get_iam_apps
from iam.utils.api import get_apis, is_iam_api
from iam.utils.requests import get_request_details


# IAM App Locals & Globals

APIS = get_apis()


def handle_request():
	iam.init(True)

	iam.request.route = route = frappe.request.path
	if not is_iam_api(route):
		return

	iam.request.is_api = True
	iam.request.environ = frappe.request.environ.copy()
	iam.request.request_details = get_request_details(False)

	iam.flags.is_jwt_enabled = is_enabled_iam_settings("jwt_auth")
	iam.flags.is_jwt_required = is_enabled_iam_settings("jwt_required")

	try:
		endpoint, arguments = APIS.bind_to_environ(frappe.request.environ).match()
	except MethodNotAllowed:
		iam.request.is_iam = iam.request.is_rest = True
		raise iam.APIMethodNotAllowedError()
	except Exception:
		pass
	else:
		app = endpoint.split(".", 1)[0]
		if app not in get_iam_apps():
			raise iam.APINotFoundError()

		iam.request.is_iam = iam.request.is_rest = True
		iam.request.endpoint = endpoint
		iam.request.path = frappe.request.path = frappe.request.environ["PATH_INFO"] = frappe.request.environ["REQUEST_URI"] = frappe.request.environ["RAW_URI"] = f"/api/method/{endpoint}"
		frappe.form_dict.update(arguments)

def handle_response(request, response):
	if iam.initialized and not iam.request.is_api:
		return
	if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
		prepare_response(response)

def prepare_response(response):
	if not iam.response:
		return

	if iam.response.http_status_code:
		status_code = iam.response.pop("http_status_code")
		response.status_code = status_code
		if status_code >= 200 and status_code < 300:
			iam.response.success = True
			iam.response.pop("exc_type", None)
			iam.response.pop("error_id", None)
		else:
			iam.response.success = False
			iam.response.pop("response_type", None)

	response.data = json.dumps(iam.local.iam_response, default=json_handler, separators=(",", ":"))
	if response.status_code == 401 or iam.flags.is_jwt_enabled:
		if hasattr(frappe.local, "cookie_manager"):
			frappe.local.cookie_manager.cookies = {}
			frappe.local.cookie_manager.to_delete = []

def handle_api_exception(error):
	response = handle_frappe_exception(error)
	iam.InternalServerError()
	iam.response.error_id = log_api_error()
	if not iam.initialized:
		iam.InternalServerError()
		iam.response.error_id = log_api_error()
		return response

	if not iam.request.is_api:
		return response

	if iam.request.is_iam or iam.response.exc_type:
		if not iam.response.exc_type:
			if response.status_code >= 500:
				iam.InternalServerError()
				iam.response.error_id = log_api_error()
			elif error.__class__ in iam.IAM_API_ERROR_MAP:
				iam.IAM_API_ERROR_MAP[error.__class__]()
			elif error.__class__ in iam.IAM_INTEGRITY_ERRORS:
				try:
					integrity_error = error.args[-1]
					integrity_values = list(filter(None, integrity_error.args[-1].split("'")))
					target = integrity_values[-1]
					value = integrity_values[1]
					iam.DuplicateEntryError(targets=target, value=value)
				except Exception:
					iam.ConflictError()
				finally:
					iam.response.error_id = log_api_error()
			else:
				iam.IAM_ERROR_MAP.get(error.__class__, iam.BadRequestError)()
				iam.response.error_id = log_api_error()
		else:
			if not isinstance(error, iam.APINotFoundError):
				iam.response.error_id = log_api_error()
	elif error.__class__ in iam.API_ERROR_MAP:
		iam.API_ERROR_MAP[error.__class__]()
	return response

def log_api_error() -> str:
	title = f"{iam.response.exc_type} - {iam.response.http_status_code} - {iam.response.specific_code} - API Error"

	request = iam.request.copy()
	request.pop("environ")

	traceback = frappe.get_traceback(with_context=True)
	traceback += f"\n {'-' * 25} \n IAM Request: \n {json.dumps(request, indent=4)}"

	error_id = frappe.log_error(title, traceback).name
	return error_id


handle_frappe_exception = frappe.app.handle_exception
frappe.app.handle_exception = handle_api_exception
