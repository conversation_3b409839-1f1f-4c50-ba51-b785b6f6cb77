import frappe, iam
from frappe import generate_hash
from frappe.utils.image import optimize_image
from mimetypes import guess_type

def upload_files(doctype, doc, filekeys, repeated_filekeys=False, field=None, optimize=True, is_private=False, ignore_permissions=True):
    # Handles the uploading of files through API and attaching them to a document
    # Validate uploaded files before calling this function through validate_uploaded_files() in validate.py
    # filekeys = list of strings of file keys if repeated_filekeys is False or
    # filekeys = string of the repeated file key if repeated_filekeys is True
    # repeated_filekeys = True: uploading multiple files with the same key, otherwise = False
    # mixing between unique and repeated file keys is not supported, call the function twice instead
    # field = "file_name": Attach the uploaded file to the given field
    # optimize = True: Optimize the file size, this works only for images

	doctype = str(doctype)
	doc = str(doc)

	files = get_files(filekeys, repeated_filekeys)
	folder = create_folders([doctype, doc])
	sort_order = get_file_sort_order(doctype, doc)
	file_docs = []

	for file in files:
		content = file.stream.read()

		file_extension = file.filename.split(".")[-1]
		if repeated_filekeys:
			file_name = f"{doctype}-{doc}-{file.name}-{generate_hash(length=5)}.{file_extension}"
		else:
			file_name = f"{doctype}-{doc}-{file.name}.{file_extension}"
		file_name = file_name.replace(" ", "-").replace("_", "-").lower()

		content_type = guess_type(file_name)[0]
		if optimize and content_type and content_type.startswith("image/"):
			content = get_optimized_image(content, content_type)

		file_doc = frappe.get_doc({
			"doctype": "File",
			"attached_to_doctype": doctype,
			"attached_to_name": doc,
			"attached_to_field": field,
			"folder": folder,
			"file_name": file_name,
			"is_private": is_private,
			"sort_order": sort_order,
			"from_api": True,
			"content": content
		}).save(ignore_permissions=ignore_permissions)
		sort_order += 1
		file_docs.append(file_doc.as_dict())
	return file_docs

def get_files(filekeys, repeated_filekeys):
    # Returns all the files sent in the request as a list

    uploaded_files = frappe.request.files
    if repeated_filekeys:
        files = uploaded_files.getlist(filekeys)
    else:
        files = [uploaded_files.get(filekey) for filekey in filekeys]
    files = [file for file in files if file] # filter empty files
    return files

def create_folders(folders):
	# Creates required folders structure and returns its path
	folder_path = "Home"
	for folder in folders:
		frappe.get_doc({
			"doctype": "File",
			"file_name": folder,
			"is_folder": 1,
			"folder": folder_path
		}).insert(ignore_if_duplicate=True, ignore_links=True)
		folder_path += f"/{folder}"
	return folder_path

def get_file_sort_order(doctype, doc):
	# Returns the latest sort order of the file

	latest_sort_order = frappe.db.get_value("File", {"attached_to_doctype": doctype, "attached_to_name": doc}, "sort_order", order_by="sort_order desc")
	sort_order = latest_sort_order + 1 if latest_sort_order else 1
	return sort_order

def reorder_files(doctype, doc, files_order=None, reset_order=False):
	# Re-orders the files attached to a document by re-setting the sort_order of each file
	# files_order -> list, list of strings of file urls of the already uploaded files
	# or empty strings for the newly uploaded files
	# if files_order is not provided, the files will be ordered by creation
	if files_order:
		files = frappe.get_all("File", {"attached_to_doctype": doctype, "attached_to_name": doc}, ["name", "file_url"], order_by="creation asc")
		for file in files:
			try:
				file_order_index = files_order.index(file.file_url)
			except Exception as error:
				file_order_index = files_order.index("")

			files_order[file_order_index] = file.name
			sort_order = file_order_index + 1
			file_doc = frappe.get_doc("File", file.name)
			file_doc.sort_order = sort_order
			file_doc.save()
	else:
		sort_order = 1
		order_by = "creation asc" if reset_order else "sort_order asc"
		files = frappe.get_all("File", {"attached_to_doctype": doctype, "attached_to_name": doc}, order_by=order_by, pluck="name")
		for file in files:
			file_doc = frappe.get_doc("File", file)
			file_doc.sort_order = sort_order
			file_doc.save()
			sort_order += 1

def get_optimized_image(content, content_type):
	# Optimizes the image and returns its content

	args = {"content": content, "content_type": content_type}
	if frappe.form_dict.max_width:
		args["max_width"] = int(frappe.form_dict.max_width)
	if frappe.form_dict.max_height:
		args["max_height"] = int(frappe.form_dict.max_height)
	content = optimize_image(**args)
	return content

def get_attached_files(doctype, doc=None):
	# Returns all the files attached to a document orderd by sort_order
	if not doc:
		return None
	files = frappe.get_all("File", {"attached_to_doctype": doctype, "attached_to_name": doc}, "file_url", order_by="sort_order asc", pluck="file_url")
	return files

def delete_attached_files(doctype, doc, to_delete=None, delete_all=False):
	# Delete all or specific files attached to a document
	# to_delete = list of strings of file urls to be deleted, leave it empty to delete all files
	files = []
	if to_delete and isinstance(to_delete, list):
		files = frappe.get_all("File", {"attached_to_doctype": doctype, "attached_to_name": doc, "file_url": ["in", to_delete]}, pluck="name") # ensures that the provided files are really attached to that document
	elif delete_all:
		files = frappe.get_all("File", {"attached_to_doctype": doctype, "attached_to_name": doc}, pluck="name")

	for file in files:
		frappe.delete_doc("File", file)

# ----------------------------
######## File Hooks ########

def write_file(file):
	set_file_details(file)
	file.save_file_on_filesystem()

def file_before_insert(file, method = None):
	# Ensures that the details are set even if the file is already written to disk
	set_file_details(file)

def set_file_details(file):
	# Sets the folder and file name of the uploaded file through admin panel
	# if not (file.is_folder or file.from_api or file.get("is_details_set")):
	# 	if file.attached_to_doctype and file.attached_to_name:
	# 		if not file.get("name_hash"):
	# 			file.name_hash = generate_hash(length=5)
	# 		file_extension = file.file_name.split(".")[-1]
	# 		file_name = f"{file.attached_to_doctype}-{file.attached_to_name}-{file.name_hash}.{file_extension}"
	# 		file_name = file_name.replace(" ", "-").replace("_", "-").lower()

	# 		file.folder = create_folders([file.attached_to_doctype, file.attached_to_name])
	# 		file.file_name = file_name
	# 		file.sort_order = get_file_sort_order(file.attached_to_doctype, file.attached_to_name)
	# 		file.is_details_set = True
	# 	else:
	# 		file.file_name = file.file_name.replace(" ", "-").replace("_", "-").lower()
	# 		file.is_details_set = True
	pass
