2025-02-09 13:23:57,247 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doc': '{"name":"LR-2025-10","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:21:23.332034","modified_by":"Administrator","docstatus":0,"idx":6,"company":"المؤسسة العامة للتكافل الإجتماعي","naming_series":"LR-.YYYY.-.#","status":"Pending Field Investigation","branch":"Cairo","creation_date":"2025-02-08","is_web_form":0,"beneficiary":"30010150101705","full_name":"test tajj3","nid":"30010150101705","dob":"2000-10-15","age":24,"gender":"Male","mobile_no_search":"01117549705","registered_address":"test","registered_governorate":"Cairo","registered_division":"الشرقية","registered_city":"النزهة","as_registered_address":"Yes","current_address":"test","current_governorate":"Cairo","current_division":"الشرقية","current_city":"النزهة","marital_status":"Single","educational_qualification":"Higher Education","handicapped":"Yes","family_member_handicapped":"No","pm_holder":"No","previous_loans":"No","loan_amount":0,"loan_monthly_installment":0,"has_meeza_card":"No","has_bank_account":"No","beneficiary_project":"bbbbb-**************-Project-1","project_name":"bbbbb","project_type":"انتاج حيواني","project_status":"New Project","project_description":"","project_address":"caaaaa","project_division":"caaaa","project_village":"","project_nagaa":"","project_city":"nasr","project_governorate":"Cairo","loan_program":"التدخل الميداني - اتفاقية 3","terms_and_conditions":"اقرار و تعهد بسداد الاقساط\\n-بأن جميع المستندات المقدمة مني الي المؤسسة العامة للتكافل الاجتماعي بغرض الحصول علي مبلغ من المال سليمة و صحيحة و مطابقة للاصل و علي مسئوليتي الخاصة و اكون متحملا المسئولية الجنائية في حالة ثبوت عدم صحتها و دون ادنى مسئولية عليهم.\\n-بأن العنوان الموضح لدى المؤسسة العامة للتكافل الاجتماعي و الخاص بمزاولة النشاط و الخاص بإقامتي هو العنوان المختار لي و يحق لهم ارسال الانذارات و المكاتبات و المراسلات عليه ... وفي حالة تغيير محل الاقامة او مزاولة النشاط التزم باخطارهم خلال اسبوع من تاريخ التغيير بخطاب مسجل بعلم الوصول او انذار وفي حالة عدم اخطارهم بتغيير محل الاقامة او مزاولة النشاط يحق لهم اعلاني علي العنوان الموضح (السابق)","loan_product":"منتج 1 - التدخل الميداني - اتفاقية 3","loan_type":"848e2e6609","lp_amount":10000,"payment_window":7,"grace_period":5,"number_of_installments":18,"interest_rate":10,"application_fees_type":"Percentage","insurance_fees_type":"Percentage","late_payment_fees_type":"Percentage","application_fees_amount":0,"application_fees_percentage":1,"insurance_fees_amount":0,"insurance_fees_percentage":1,"late_payment_fees_amount":0,"late_payment_fees_percentage":0,"early_payment_fees_percentage":5,"repayment_total":11500,"monthly_installment":640,"application_fees_loan_amount":100,"interest_total":1500,"last_monthly_installment":620,"insurance_fees_loan_amount":100,"guarantor":"29810150101716","guarantor_full_name":"tessst tajj2","guarantor_nid":"29810150101716","guarantor_dob":"1998-10-15","guarantor_gender":"Male","guarantor_relation":"aaaa","guarantor_registered_address":"tgdhh","guarantor_registered_governorate":"Cairo","guarantor_registered_division":"الشرقية","guarantor_registered_city":"النزهة","guarantor_as_registered_address":"Yes","guarantor_current_address":"tgdhh","guarantor_current_governorate":"Cairo","guarantor_current_division":"الشرقية","guarantor_current_city":"النزهة","attachments_template":"Loan Request - Test","iscore_status":"Failed","iscore_report_id":"2682d3694c","iscore_doc":"ISC-2025-02-08-3","iscore_report":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png","iscore_result":50,"doctype":"Loan Request","social_investigations":[],"mobile_nos":[{"name":"0b027v7cgi","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:21:23.332034","modified_by":"Administrator","docstatus":0,"idx":1,"mobile_no":"01117549705","parent":"LR-2025-10","parentfield":"mobile_nos","parenttype":"Loan Request","doctype":"Loan Request Mobile Number"}],"guarantor_mobile_nos":[{"name":"hkuuecuuuo","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:21:23.332034","modified_by":"Administrator","docstatus":0,"idx":1,"mobile_no":"01224851716","parent":"LR-2025-10","parentfield":"guarantor_mobile_nos","parenttype":"Loan Request","doctype":"Loan Request Mobile Number"}],"incomes":[{"name":"fjck4cv43u","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:21:23.332034","modified_by":"Administrator","docstatus":0,"idx":1,"type":"مرتب","amount":10000,"frequency":"Monthly","parent":"LR-2025-10","parentfield":"incomes","parenttype":"Loan Request","doctype":"Income Details"}],"family_member_disability":[{"name":"sjcs2fg30s","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:21:23.332034","modified_by":"Administrator","docstatus":0,"idx":2,"disability":"Dwarfism","parent":"LR-2025-10","parentfield":"family_member_disability","parenttype":"Loan Request","doctype":"Beneficiary Disability"}],"attachments":[{"name":"v0bjgak5jf","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:21:23.332034","modified_by":"Administrator","docstatus":0,"idx":1,"attachment":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png","attachment_id":"7dd700634a","type":"Beneficiary National ID Front","has_expiry_date":"Yes","linked_to":"Beneficiary","expiry_date":"2025-02-28","parent":"LR-2025-10","parentfield":"attachments","parenttype":"Loan Request","doctype":"Attachment"},{"name":"0p27e6bo9v","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:21:23.332034","modified_by":"Administrator","docstatus":0,"idx":2,"attachment":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png","attachment_id":"af86fbd8f1","type":"Beneficiary National ID Front","has_expiry_date":"Yes","linked_to":"Beneficiary","expiry_date":"2025-02-28","parent":"LR-2025-10","parentfield":"attachments","parenttype":"Loan Request","doctype":"Attachment"}],"feasibility_studies":[],"disability":[{"name":"v3k6q3lv0k","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:21:23.332034","modified_by":"Administrator","docstatus":0,"idx":1,"disability":"Autism Spectrum Disorder","parent":"LR-2025-10","parentfield":"disability","parenttype":"Loan Request","doctype":"Beneficiary Disability"},{"name":"hiqkh9ko2j","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:21:23.332034","modified_by":"Administrator","docstatus":0,"idx":2,"disability":"Blood Diseases","parent":"LR-2025-10","parentfield":"disability","parenttype":"Loan Request","doctype":"Beneficiary Disability"}],"extra_documents":[],"__onload":{"field_investigations":{"feasibility_study":{"permissions":{"has_read":true,"has_create":true,"has_delete":true},"current_investigation":null},"social_investigation":{"permissions":{"has_read":true,"has_create":true,"has_delete":true},"current_investigation":null}}},"__last_sync_on":"2025-02-09T11:21:24.139Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-02-09 13:25:10,498 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doc': '{"name":"LR-2025-10","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:24:53.255237","modified_by":"Administrator","docstatus":0,"idx":6,"company":"المؤسسة العامة للتكافل الإجتماعي","naming_series":"LR-.YYYY.-.#","status":"Pending Field Investigation","branch":"Cairo","creation_date":"2025-02-08","is_web_form":0,"beneficiary":"30010150101705","full_name":"test tajj3","nid":"30010150101705","dob":"2000-10-15","age":24,"gender":"Male","mobile_no_search":"01117549705","registered_address":"test","registered_governorate":"Cairo","registered_division":"الشرقية","registered_city":"النزهة","as_registered_address":"Yes","current_address":"test","current_governorate":"Cairo","current_division":"الشرقية","current_city":"النزهة","marital_status":"Single","educational_qualification":"Higher Education","handicapped":"Yes","family_member_handicapped":"No","pm_holder":"No","previous_loans":"No","loan_amount":0,"loan_monthly_installment":0,"has_meeza_card":"No","has_bank_account":"No","beneficiary_project":"bbbbb-**************-Project-1","project_name":"bbbbb","project_type":"انتاج حيواني","project_status":"New Project","project_description":"","project_address":"caaaaa","project_division":"caaaa","project_village":"","project_nagaa":"","project_city":"nasr","project_governorate":"Cairo","loan_program":"التدخل الميداني - اتفاقية 3","terms_and_conditions":"اقرار و تعهد بسداد الاقساط\\n-بأن جميع المستندات المقدمة مني الي المؤسسة العامة للتكافل الاجتماعي بغرض الحصول علي مبلغ من المال سليمة و صحيحة و مطابقة للاصل و علي مسئوليتي الخاصة و اكون متحملا المسئولية الجنائية في حالة ثبوت عدم صحتها و دون ادنى مسئولية عليهم.\\n-بأن العنوان الموضح لدى المؤسسة العامة للتكافل الاجتماعي و الخاص بمزاولة النشاط و الخاص بإقامتي هو العنوان المختار لي و يحق لهم ارسال الانذارات و المكاتبات و المراسلات عليه ... وفي حالة تغيير محل الاقامة او مزاولة النشاط التزم باخطارهم خلال اسبوع من تاريخ التغيير بخطاب مسجل بعلم الوصول او انذار وفي حالة عدم اخطارهم بتغيير محل الاقامة او مزاولة النشاط يحق لهم اعلاني علي العنوان الموضح (السابق)","loan_product":"منتج 1 - التدخل الميداني - اتفاقية 3","loan_type":"848e2e6609","lp_amount":10000,"payment_window":7,"grace_period":5,"number_of_installments":18,"interest_rate":10,"application_fees_type":"Percentage","insurance_fees_type":"Percentage","late_payment_fees_type":"Percentage","application_fees_amount":0,"application_fees_percentage":1,"insurance_fees_amount":0,"insurance_fees_percentage":1,"late_payment_fees_amount":0,"late_payment_fees_percentage":0,"early_payment_fees_percentage":5,"repayment_total":11500,"monthly_installment":640,"application_fees_loan_amount":100,"interest_total":1500,"last_monthly_installment":620,"insurance_fees_loan_amount":100,"guarantor":"29810150101716","guarantor_full_name":"tessst tajj2","guarantor_nid":"29810150101716","guarantor_dob":"1998-10-15","guarantor_gender":"Male","guarantor_relation":"aaaa","guarantor_registered_address":"tgdhh","guarantor_registered_governorate":"Cairo","guarantor_registered_division":"الشرقية","guarantor_registered_city":"النزهة","guarantor_as_registered_address":"Yes","guarantor_current_address":"tgdhh","guarantor_current_governorate":"Cairo","guarantor_current_division":"الشرقية","guarantor_current_city":"النزهة","attachments_template":"Loan Request - Test","iscore_status":"Failed","iscore_report_id":"2682d3694c","iscore_doc":"ISC-2025-02-08-3","iscore_report":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png","iscore_result":50,"doctype":"Loan Request","guarantor_mobile_nos":[{"name":"hkuuecuuuo","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:24:53.255237","modified_by":"Administrator","docstatus":0,"idx":1,"mobile_no":"01224851716","parent":"LR-2025-10","parentfield":"guarantor_mobile_nos","parenttype":"Loan Request","doctype":"Loan Request Mobile Number"}],"disability":[{"name":"v3k6q3lv0k","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:24:53.255237","modified_by":"Administrator","docstatus":0,"idx":1,"disability":"Autism Spectrum Disorder","parent":"LR-2025-10","parentfield":"disability","parenttype":"Loan Request","doctype":"Beneficiary Disability"},{"name":"hiqkh9ko2j","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:24:53.255237","modified_by":"Administrator","docstatus":0,"idx":2,"disability":"Blood Diseases","parent":"LR-2025-10","parentfield":"disability","parenttype":"Loan Request","doctype":"Beneficiary Disability"}],"mobile_nos":[{"name":"0b027v7cgi","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:24:53.255237","modified_by":"Administrator","docstatus":0,"idx":1,"mobile_no":"01117549705","parent":"LR-2025-10","parentfield":"mobile_nos","parenttype":"Loan Request","doctype":"Loan Request Mobile Number"}],"attachments":[{"name":"v0bjgak5jf","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:24:53.255237","modified_by":"Administrator","docstatus":0,"idx":1,"attachment":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png","attachment_id":"7dd700634a","type":"Beneficiary National ID Front","has_expiry_date":"Yes","linked_to":"Beneficiary","expiry_date":"2025-02-28","parent":"LR-2025-10","parentfield":"attachments","parenttype":"Loan Request","doctype":"Attachment"},{"name":"0p27e6bo9v","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:24:53.255237","modified_by":"Administrator","docstatus":0,"idx":2,"attachment":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png","attachment_id":"af86fbd8f1","type":"Beneficiary National ID Front","has_expiry_date":"Yes","linked_to":"Beneficiary","expiry_date":"2025-02-28","parent":"LR-2025-10","parentfield":"attachments","parenttype":"Loan Request","doctype":"Attachment"}],"incomes":[{"name":"fjck4cv43u","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:24:53.255237","modified_by":"Administrator","docstatus":0,"idx":1,"type":"مرتب","amount":10000,"frequency":"Monthly","parent":"LR-2025-10","parentfield":"incomes","parenttype":"Loan Request","doctype":"Income Details"}],"family_member_disability":[{"name":"sjcs2fg30s","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:24:53.255237","modified_by":"Administrator","docstatus":0,"idx":2,"disability":"Dwarfism","parent":"LR-2025-10","parentfield":"family_member_disability","parenttype":"Loan Request","doctype":"Beneficiary Disability"}],"feasibility_studies":[],"social_investigations":[],"extra_documents":[],"__onload":{"field_investigations":{"feasibility_study":{"permissions":{"has_read":true,"has_create":true,"has_delete":true},"current_investigation":null},"social_investigation":{"permissions":{"has_read":true,"has_create":true,"has_delete":true},"current_investigation":null}}},"__last_sync_on":"2025-02-09T11:24:54.684Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-02-09 14:09:58,029 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doc': '{"name":"LR-2025-10","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:34:29.663109","modified_by":"Administrator","docstatus":0,"idx":6,"company":"المؤسسة العامة للتكافل الإجتماعي","naming_series":"LR-.YYYY.-.#","status":"Pending Field Investigation","branch":"Cairo","creation_date":"2025-02-08","is_web_form":0,"beneficiary":"30010150101705","full_name":"test tajj3","nid":"30010150101705","dob":"2000-10-15","age":24,"gender":"Male","mobile_no_search":"01117549705","registered_address":"test","registered_governorate":"Cairo","registered_division":"الشرقية","registered_city":"النزهة","as_registered_address":"Yes","current_address":"test","current_governorate":"Cairo","current_division":"الشرقية","current_city":"النزهة","marital_status":"Single","educational_qualification":"Higher Education","handicapped":"Yes","family_member_handicapped":"No","pm_holder":"No","previous_loans":"No","loan_amount":0,"loan_monthly_installment":0,"has_meeza_card":"No","has_bank_account":"No","beneficiary_project":"bbbbb-**************-Project-1","project_name":"bbbbb","project_type":"انتاج حيواني","project_status":"New Project","project_description":"","project_address":"caaaaa","project_division":"caaaa","project_village":"","project_nagaa":"","project_city":"nasr","project_governorate":"Cairo","loan_program":"التدخل الميداني - اتفاقية 3","terms_and_conditions":"اقرار و تعهد بسداد الاقساط\\n-بأن جميع المستندات المقدمة مني الي المؤسسة العامة للتكافل الاجتماعي بغرض الحصول علي مبلغ من المال سليمة و صحيحة و مطابقة للاصل و علي مسئوليتي الخاصة و اكون متحملا المسئولية الجنائية في حالة ثبوت عدم صحتها و دون ادنى مسئولية عليهم.\\n-بأن العنوان الموضح لدى المؤسسة العامة للتكافل الاجتماعي و الخاص بمزاولة النشاط و الخاص بإقامتي هو العنوان المختار لي و يحق لهم ارسال الانذارات و المكاتبات و المراسلات عليه ... وفي حالة تغيير محل الاقامة او مزاولة النشاط التزم باخطارهم خلال اسبوع من تاريخ التغيير بخطاب مسجل بعلم الوصول او انذار وفي حالة عدم اخطارهم بتغيير محل الاقامة او مزاولة النشاط يحق لهم اعلاني علي العنوان الموضح (السابق)","loan_product":"منتج 1 - التدخل الميداني - اتفاقية 3","loan_type":"848e2e6609","lp_amount":10000,"payment_window":7,"grace_period":5,"number_of_installments":18,"interest_rate":10,"application_fees_type":"Percentage","insurance_fees_type":"Percentage","late_payment_fees_type":"Percentage","application_fees_amount":0,"application_fees_percentage":1,"insurance_fees_amount":0,"insurance_fees_percentage":1,"late_payment_fees_amount":0,"late_payment_fees_percentage":0,"early_payment_fees_percentage":5,"repayment_total":11500,"monthly_installment":640,"application_fees_loan_amount":100,"interest_total":1500,"last_monthly_installment":620,"insurance_fees_loan_amount":100,"guarantor":"29810150101716","guarantor_full_name":"tessst tajj2","guarantor_nid":"29810150101716","guarantor_dob":"1998-10-15","guarantor_gender":"Male","guarantor_relation":"aaaa","guarantor_registered_address":"tgdhh","guarantor_registered_governorate":"Cairo","guarantor_registered_division":"الشرقية","guarantor_registered_city":"النزهة","guarantor_as_registered_address":"Yes","guarantor_current_address":"tgdhh","guarantor_current_governorate":"Cairo","guarantor_current_division":"الشرقية","guarantor_current_city":"النزهة","attachments_template":"Loan Request - Test","iscore_status":"Failed","iscore_report_id":"2682d3694c","iscore_doc":"ISC-2025-02-08-3","iscore_report":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png","iscore_result":50,"doctype":"Loan Request","family_member_disability":[{"name":"u1etcrg4ku","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:34:29.663109","modified_by":"Administrator","docstatus":0,"idx":1,"disability":"أمراض الدم","parent":"LR-2025-10","parentfield":"family_member_disability","parenttype":"Loan Request","doctype":"Beneficiary Disability"},{"name":"dp0hlhckjb","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:34:29.663109","modified_by":"Administrator","docstatus":0,"idx":2,"disability":"Autism Spectrum Disorder","parent":"LR-2025-10","parentfield":"family_member_disability","parenttype":"Loan Request","doctype":"Beneficiary Disability"},{"name":"0mv9t868mf","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:34:29.663109","modified_by":"Administrator","docstatus":0,"idx":3,"disability":"القزامة","parent":"LR-2025-10","parentfield":"family_member_disability","parenttype":"Loan Request","doctype":"Beneficiary Disability"}],"extra_documents":[],"social_investigations":[],"incomes":[{"name":"ve00fs49d1","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:34:29.663109","modified_by":"Administrator","docstatus":0,"idx":1,"type":"مرتب","amount":10000,"frequency":"Monthly","parent":"LR-2025-10","parentfield":"incomes","parenttype":"Loan Request","doctype":"Income Details"}],"mobile_nos":[{"name":"3vis2rnn4s","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:34:29.663109","modified_by":"Administrator","docstatus":0,"idx":1,"mobile_no":"01117549705","parent":"LR-2025-10","parentfield":"mobile_nos","parenttype":"Loan Request","doctype":"Loan Request Mobile Number"}],"guarantor_mobile_nos":[{"name":"hkuuecuuuo","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:34:29.663109","modified_by":"Administrator","docstatus":0,"idx":1,"mobile_no":"01224851716","parent":"LR-2025-10","parentfield":"guarantor_mobile_nos","parenttype":"Loan Request","doctype":"Loan Request Mobile Number"}],"feasibility_studies":[],"disability":[{"name":"e8l5tbfoef","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:34:29.663109","modified_by":"Administrator","docstatus":0,"idx":1,"disability":"Blood Diseases","parent":"LR-2025-10","parentfield":"disability","parenttype":"Loan Request","doctype":"Beneficiary Disability"},{"name":"urp1osra1u","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:34:29.663109","modified_by":"Administrator","docstatus":0,"idx":2,"disability":"Autism Spectrum Disorder","parent":"LR-2025-10","parentfield":"disability","parenttype":"Loan Request","doctype":"Beneficiary Disability"}],"attachments":[{"name":"v0bjgak5jf","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:34:29.663109","modified_by":"Administrator","docstatus":0,"idx":1,"attachment":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png","attachment_id":"7dd700634a","type":"Beneficiary National ID Front","has_expiry_date":"Yes","linked_to":"Beneficiary","expiry_date":"2025-02-28","parent":"LR-2025-10","parentfield":"attachments","parenttype":"Loan Request","doctype":"Attachment"},{"name":"0p27e6bo9v","owner":"Administrator","creation":"2025-02-08 14:27:05.688488","modified":"2025-02-09 13:34:29.663109","modified_by":"Administrator","docstatus":0,"idx":2,"attachment":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png","attachment_id":"af86fbd8f1","type":"Beneficiary National ID Front","has_expiry_date":"Yes","linked_to":"Beneficiary","expiry_date":"2025-02-28","parent":"LR-2025-10","parentfield":"attachments","parenttype":"Loan Request","doctype":"Attachment"}],"__onload":{"field_investigations":{"feasibility_study":{"permissions":{"has_read":true,"has_create":true,"has_delete":true},"current_investigation":null},"social_investigation":{"permissions":{"has_read":true,"has_create":true,"has_delete":true},"current_investigation":null}}},"__last_sync_on":"2025-02-09T11:34:30.301Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-02-09 14:32:46,074 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'national_id': '30010150101222', 'cmd': 'ngo.api.loan_request.get_beneficiary'}
2025-02-09 14:35:36,478 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'national_id': '30010150101222', 'cmd': 'ngo.api.loan_request.get_beneficiary'}
2025-02-09 14:47:50,765 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'national_id': '30010150101222', 'cmd': 'ngo.api.loan_request.get_beneficiary'}
2025-02-09 14:48:21,845 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'national_id': '30010150101222', 'cmd': 'ngo.api.loan_request.get_beneficiary'}
2025-02-09 14:51:45,943 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'national_id': '30010150101222', 'cmd': 'ngo.api.loan_request.get_beneficiary'}
2025-02-09 14:52:21,701 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'national_id': '30010150101222', 'cmd': 'ngo.api.loan_request.get_beneficiary'}
2025-02-09 14:58:01,827 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'national_id': '30010150101222', 'cmd': 'ngo.api.loan_request.get_beneficiary'}
2025-02-09 15:01:41,351 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'national_id': '30010150101222', 'cmd': 'ngo.api.loan_request.get_beneficiary'}
2025-02-09 15:02:55,955 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'national_id': '30010150101222', 'cmd': 'ngo.api.loan_request.get_beneficiary'}
2025-02-09 15:29:33,415 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'method': 'has_loan_permissions', 'cmd': 'run_doc_method'}
2025-02-09 15:40:37,686 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doc': '{"name":"<EMAIL>","owner":"Administrator","creation":"2023-12-12 14:47:37.537011","modified":"2024-10-19 12:39:41.519850","modified_by":"Administrator","docstatus":0,"idx":0,"enabled":1,"email":"<EMAIL>","first_name":"Investigator","full_name":"Investigator","username":"investigator","language":"ar","time_zone":"Africa/Cairo","send_welcome_email":0,"unsubscribed":0,"role_profile_name":"","module_profile":"Loan Officer","mute_sounds":0,"desk_theme":"Light","search_bar":0,"notifications":0,"list_sidebar":1,"bulk_actions":1,"view_switcher":1,"form_sidebar":1,"timeline":1,"dashboard":1,"new_password":"","logout_all_sessions":1,"document_follow_notify":0,"document_follow_frequency":"Daily","follow_created_documents":0,"follow_commented_documents":0,"follow_liked_documents":0,"follow_assigned_documents":0,"follow_shared_documents":0,"thread_notify":1,"send_me_a_copy":0,"allowed_in_mentions":1,"simultaneous_sessions":1,"last_ip":"127.0.0.1","login_after":0,"user_type":"Website User","last_active":"2025-02-09 15:27:02.836446","login_before":0,"bypass_restrict_ip_check_if_2fa_enabled":0,"last_login":"2025-02-09 15:27:02.483523","last_known_versions":"{\\"frappe\\": {\\"title\\": \\"Frappe Framework\\", \\"description\\": \\"Full stack web framework with Python, Javascript, MariaDB, Redis, Node\\", \\"branch\\": \\"version-14\\", \\"version\\": \\"14.55.1\\"}, \\"erpnext\\": {\\"title\\": \\"ERPNext\\", \\"description\\": \\"ERP made simple\\", \\"branch\\": \\"version-14\\", \\"version\\": \\"14.48.1\\"}, \\"ngo\\": {\\"title\\": \\"NGO\\", \\"description\\": \\"This application, built on the Frappe Framework, is tailored specifically for Non-Governmental Organizations by BrainWise to streamline and enhance their operational processes.\\", \\"branch\\": \\"dev\\", \\"version\\": \\"0.1.3\\"}}","api_key":"96233bb16e3a1e0","api_secret":"***************","onboarding_status":"{\\"Main Workspace Tour\\":{\\"is_complete\\":true},\\"User List Tour\\":{\\"steps_complete\\":1,\\"is_complete\\":true}}","doctype":"User","user_emails":[],"defaults":[],"roles":[{"name":"5rtatrj0cq","owner":"Administrator","creation":"2024-10-19 12:39:41.854283","modified":"2024-10-19 12:39:41.854283","modified_by":"Administrator","docstatus":0,"idx":1,"role":"NGO Investigator","parent":"<EMAIL>","parentfield":"roles","parenttype":"User","doctype":"Has Role"},{"name":"5rta2frk26","owner":"Administrator","creation":"2024-10-19 12:39:41.881052","modified":"2024-10-19 12:39:41.881052","modified_by":"Administrator","docstatus":0,"idx":2,"role":"Authenticated User","parent":"<EMAIL>","parentfield":"roles","parenttype":"User","doctype":"Has Role"}],"social_logins":[{"name":"4248f46c4f","owner":"Administrator","creation":"2023-12-12 14:47:37.799324","modified":"2024-10-19 12:39:41.519850","modified_by":"Administrator","docstatus":0,"idx":1,"provider":"frappe","userid":"035a6ef246a7922e31bc0b146e3a90d6faf69f5","parent":"<EMAIL>","parentfield":"social_logins","parenttype":"User","doctype":"User Social Login"}],"block_modules":[{"name":"5rta6gj72s","owner":"Administrator","creation":"2024-10-19 12:39:41.884394","modified":"2024-10-19 12:39:41.884394","modified_by":"Administrator","docstatus":0,"idx":1,"module":"Assets","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rta0qagjq","owner":"Administrator","creation":"2024-10-19 12:39:41.884671","modified":"2024-10-19 12:39:41.884671","modified_by":"Administrator","docstatus":0,"idx":2,"module":"Automation","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtadoo4qu","owner":"Administrator","creation":"2024-10-19 12:39:41.884889","modified":"2024-10-19 12:39:41.884889","modified_by":"Administrator","docstatus":0,"idx":3,"module":"Bulk Transaction","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtal1vmeu","owner":"Administrator","creation":"2024-10-19 12:39:41.885094","modified":"2024-10-19 12:39:41.885094","modified_by":"Administrator","docstatus":0,"idx":4,"module":"Buying","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtaqmv2ca","owner":"Administrator","creation":"2024-10-19 12:39:41.885296","modified":"2024-10-19 12:39:41.885296","modified_by":"Administrator","docstatus":0,"idx":5,"module":"CRM","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtahvsdqu","owner":"Administrator","creation":"2024-10-19 12:39:41.885531","modified":"2024-10-19 12:39:41.885531","modified_by":"Administrator","docstatus":0,"idx":6,"module":"Communication","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rta5nj0c4","owner":"Administrator","creation":"2024-10-19 12:39:41.885745","modified":"2024-10-19 12:39:41.885745","modified_by":"Administrator","docstatus":0,"idx":7,"module":"Contacts","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rta6f2i78","owner":"Administrator","creation":"2024-10-19 12:39:41.885969","modified":"2024-10-19 12:39:41.885969","modified_by":"Administrator","docstatus":0,"idx":8,"module":"Core","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtaslgqk4","owner":"Administrator","creation":"2024-10-19 12:39:41.886155","modified":"2024-10-19 12:39:41.886155","modified_by":"Administrator","docstatus":0,"idx":9,"module":"Custom","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtaeokmjr","owner":"Administrator","creation":"2024-10-19 12:39:41.886343","modified":"2024-10-19 12:39:41.886343","modified_by":"Administrator","docstatus":0,"idx":10,"module":"E-commerce","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rta6ofnsk","owner":"Administrator","creation":"2024-10-19 12:39:41.886537","modified":"2024-10-19 12:39:41.886537","modified_by":"Administrator","docstatus":0,"idx":11,"module":"ERPNext Integrations","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtau5olcp","owner":"Administrator","creation":"2024-10-19 12:39:41.886738","modified":"2024-10-19 12:39:41.886738","modified_by":"Administrator","docstatus":0,"idx":12,"module":"Email","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtan1jben","owner":"Administrator","creation":"2024-10-19 12:39:41.886925","modified":"2024-10-19 12:39:41.886925","modified_by":"Administrator","docstatus":0,"idx":13,"module":"Event Streaming","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtafj9epl","owner":"Administrator","creation":"2024-10-19 12:39:41.887110","modified":"2024-10-19 12:39:41.887110","modified_by":"Administrator","docstatus":0,"idx":14,"module":"Geo","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rta71vt48","owner":"Administrator","creation":"2024-10-19 12:39:41.887294","modified":"2024-10-19 12:39:41.887294","modified_by":"Administrator","docstatus":0,"idx":15,"module":"Integrations","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtajriicn","owner":"Administrator","creation":"2024-10-19 12:39:41.887481","modified":"2024-10-19 12:39:41.887481","modified_by":"Administrator","docstatus":0,"idx":16,"module":"Maintenance","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtablc697","owner":"Administrator","creation":"2024-10-19 12:39:41.887667","modified":"2024-10-19 12:39:41.887667","modified_by":"Administrator","docstatus":0,"idx":17,"module":"Manufacturing","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rta3lblrp","owner":"Administrator","creation":"2024-10-19 12:39:41.887856","modified":"2024-10-19 12:39:41.887856","modified_by":"Administrator","docstatus":0,"idx":18,"module":"Portal","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rta7ovnih","owner":"Administrator","creation":"2024-10-19 12:39:41.888040","modified":"2024-10-19 12:39:41.888040","modified_by":"Administrator","docstatus":0,"idx":19,"module":"Printing","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtatmmsqf","owner":"Administrator","creation":"2024-10-19 12:39:41.888226","modified":"2024-10-19 12:39:41.888226","modified_by":"Administrator","docstatus":0,"idx":20,"module":"Projects","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtajsob0g","owner":"Administrator","creation":"2024-10-19 12:39:41.888442","modified":"2024-10-19 12:39:41.888442","modified_by":"Administrator","docstatus":0,"idx":21,"module":"Quality Management","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtam9vp3u","owner":"Administrator","creation":"2024-10-19 12:39:41.888632","modified":"2024-10-19 12:39:41.888632","modified_by":"Administrator","docstatus":0,"idx":22,"module":"Regional","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtah1bt2c","owner":"Administrator","creation":"2024-10-19 12:39:41.888822","modified":"2024-10-19 12:39:41.888822","modified_by":"Administrator","docstatus":0,"idx":23,"module":"Selling","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rta9ialkj","owner":"Administrator","creation":"2024-10-19 12:39:41.889004","modified":"2024-10-19 12:39:41.889004","modified_by":"Administrator","docstatus":0,"idx":24,"module":"Social","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtarm3rhe","owner":"Administrator","creation":"2024-10-19 12:39:41.889190","modified":"2024-10-19 12:39:41.889190","modified_by":"Administrator","docstatus":0,"idx":25,"module":"Stock","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtar4qosd","owner":"Administrator","creation":"2024-10-19 12:39:41.889372","modified":"2024-10-19 12:39:41.889372","modified_by":"Administrator","docstatus":0,"idx":26,"module":"Subcontracting","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rta954cce","owner":"Administrator","creation":"2024-10-19 12:39:41.889563","modified":"2024-10-19 12:39:41.889563","modified_by":"Administrator","docstatus":0,"idx":27,"module":"Support","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtau00qva","owner":"Administrator","creation":"2024-10-19 12:39:41.889750","modified":"2024-10-19 12:39:41.889750","modified_by":"Administrator","docstatus":0,"idx":28,"module":"Telephony","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtapb65d4","owner":"Administrator","creation":"2024-10-19 12:39:41.889931","modified":"2024-10-19 12:39:41.889931","modified_by":"Administrator","docstatus":0,"idx":29,"module":"Utilities","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtacrau5f","owner":"Administrator","creation":"2024-10-19 12:39:41.890113","modified":"2024-10-19 12:39:41.890113","modified_by":"Administrator","docstatus":0,"idx":30,"module":"Website","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtaeu657q","owner":"Administrator","creation":"2024-10-19 12:39:41.890295","modified":"2024-10-19 12:39:41.890295","modified_by":"Administrator","docstatus":0,"idx":31,"module":"Workflow","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtaocfd25","owner":"Administrator","creation":"2024-10-19 12:39:41.890482","modified":"2024-10-19 12:39:41.890482","modified_by":"Administrator","docstatus":0,"idx":32,"module":"Accounts","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtauje8bm","owner":"Administrator","creation":"2024-10-19 12:39:41.890668","modified":"2024-10-19 12:39:41.890668","modified_by":"Administrator","docstatus":0,"idx":33,"module":"Setup","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"},{"name":"5rtasm20jn","owner":"Administrator","creation":"2024-10-19 12:39:41.890850","modified":"2024-10-19 12:39:41.890850","modified_by":"Administrator","docstatus":0,"idx":34,"module":"Loan Management","parent":"<EMAIL>","parentfield":"block_modules","parenttype":"User","doctype":"Block Module"}],"__onload":{"all_modules":["Accounts","Assets","Attachments","Automation","Bulk Transaction","Buying","CRM","Communication","Contacts","Core","Custom","Desk","E-commerce","EDI","ERPNext Integrations","Email","Emails","Geo","IAM","Integrations","License Manager Client","Maintenance","Manufacturing","NGO","NGO Loan","Payment Gateways","Payments","Policies","Portal","Printing","Projects","Quality Management","Regional","Selling","Setup","Social","Stock","Subcontracting","Support","Telephony","Utilities","Website","Workflow","iScore"]},"__last_sync_on":"2025-02-09T13:39:37.198Z","__unsaved":1}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-02-09 17:31:42,135 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'loan_request_id': 'LR-2025-1', 'handicapped': 'Yes', 'disability': '["أمراض الدم"]', 'cmd': 'ngo.api.loan_request.update_loan_request'}
2025-02-09 17:32:14,894 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'loan_request_id': 'LR-2025-1', 'handicapped': 'Yes', 'disability': 'أمراض الدم', 'cmd': 'ngo.api.loan_request.update_loan_request'}
2025-02-10 11:21:31,067 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'loan_request_id': 'LR-2025-1', 'handicapped': 'Yes', 'disability': '["إعاقة بصرية","أمراض الدم"]', 'cmd': 'ngo.api.loan_request.update_loan_request'}
2025-02-10 11:27:36,371 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'loan_request_id': 'LR-2025-1', 'handicapped': 'Yes', 'disability': 'أمراض الدم', 'cmd': 'ngo.api.loan_request.update_loan_request'}
2025-02-10 15:55:39,566 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'national_id': '28712242700321', 'cmd': 'ngo.api.loan_request.get_beneficiary'}
2025-02-10 15:56:00,579 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'national_id': '28712242700321', 'cmd': 'ngo.api.loan_request.get_beneficiary'}
2025-02-10 16:03:17,372 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'loan_request_id': 'LR-2025-1', 'cmd': 'ngo.api.loan_request.get_loan_request_data'}
2025-02-10 16:07:00,514 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'loan_request_id': 'LR-2025-1', 'cmd': 'ngo.api.loan_request.get_loan_request_data'}
2025-02-19 15:02:33,669 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'reference_doctype': 'Loan Request', 'reference_name': 'LR-2025-1', 'content': '<div class="ql-editor read-mode"><p>s</p><p><br></p></div>', 'comment_email': 'Administrator', 'comment_by': 'Administrator', 'cmd': 'frappe.desk.form.utils.add_comment'}
2025-02-19 16:17:37,608 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doctype': 'Beneficiary', 'name': '28808221301737', 'cmd': 'frappe.desk.form.load.getdoc'}
2025-02-19 16:23:08,024 ERROR frappe Error while inserting deferred Error Log record: Error Log berhd4b4cn: 'Title' (Module import failed for Beneficiary Disability, the DocType you're trying to open might be deleted.
Error: No module named 'ngo.ngo.doctype.beneficiary_disability.beneficiary_disability') will get truncated, as max characters allowed is 140
Site: child_ngo
Form Dict: {}
2025-02-19 16:56:39,698 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'method': 'has_loan_permissions', 'cmd': 'run_doc_method'}
2025-02-20 16:54:17,181 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'method': 'has_loan_permissions', 'cmd': 'run_doc_method'}
2025-02-21 20:14:35,163 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'docs': '{"docstatus":0,"doctype":"Guarantor","name":"new-guarantor-gkiidusgyb","__islocal":1,"__unsaved":1,"owner":"Administrator","mobile_nos":[{"docstatus":0,"doctype":"Guarantor Mobile Number","name":"new-guarantor-mobile-number-jhdiewhllo","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-guarantor-gkiidusgyb","parentfield":"mobile_nos","parenttype":"Guarantor","idx":1}],"as_registered_address":"No","nid":"**************"}', 'method': 'saybl7', 'cmd': 'run_doc_method'}
2025-02-21 22:29:35,932 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'docs': '{"docstatus":0,"doctype":"Beneficiary","name":"new-beneficiary-ygxhvjqaad","__islocal":1,"__unsaved":1,"owner":"Administrator","mobile_nos":[{"docstatus":0,"doctype":"Beneficiary Mobile Number","name":"new-beneficiary-mobile-number-mjdoygjrde","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-beneficiary-ygxhvjqaad","parentfield":"mobile_nos","parenttype":"Beneficiary","idx":1,"mobile_no":"***********","creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null}],"as_registered_address":"Yes","marital_status":"Single","educational_qualification":"Higher Education","handicapped":"No","disability":[],"family_member_handicapped":"No","family_member_disability":[],"pm_holder":"No","previous_loans":"No","loan_amount":0,"incomes":[{"docstatus":0,"doctype":"Income Details","name":"new-income-details-yemiiovoae","__islocal":1,"__unsaved":1,"owner":"Administrator","type":"مرتب","frequency":"Monthly","parent":"new-beneficiary-ygxhvjqaad","parentfield":"incomes","parenttype":"Beneficiary","idx":1,"amount":10000,"creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null}],"has_meeza_card":"No","has_bank_account":"No","__run_link_triggers":false,"full_name":"test151","nid":"**************","dob":"2000-10-15","age":24,"gender":"Male","mobile_no_search":"***********","registered_address":"test","registered_governorate":"Cairo","registered_division":"الشرقية","registered_city":"النزهة","current_address":"test","current_governorate":"Cairo","current_division":"الشرقية","current_city":"النزهة","loan_monthly_installment":0,"creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null,"idx":null}', 'method': 'extract_nid_data', 'cmd': 'run_doc_method'}
2025-02-21 22:32:46,378 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'docs': '{"docstatus":0,"doctype":"Beneficiary","name":"new-beneficiary-ygxhvjqaad","__islocal":1,"__unsaved":1,"owner":"Administrator","mobile_nos":[{"docstatus":0,"doctype":"Beneficiary Mobile Number","name":"new-beneficiary-mobile-number-mjdoygjrde","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-beneficiary-ygxhvjqaad","parentfield":"mobile_nos","parenttype":"Beneficiary","idx":1,"mobile_no":"***********","creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null}],"as_registered_address":"Yes","marital_status":"Single","educational_qualification":"Higher Education","handicapped":"No","disability":[],"family_member_handicapped":"No","family_member_disability":[],"pm_holder":"No","previous_loans":"No","loan_amount":0,"incomes":[{"docstatus":0,"doctype":"Income Details","name":"new-income-details-yemiiovoae","__islocal":1,"__unsaved":1,"owner":"Administrator","type":"مرتب","frequency":"Monthly","parent":"new-beneficiary-ygxhvjqaad","parentfield":"incomes","parenttype":"Beneficiary","idx":1,"amount":10000,"creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null}],"has_meeza_card":"No","has_bank_account":"No","__run_link_triggers":false,"full_name":"test151","nid":"**************","dob":"2000-10-15","age":24,"gender":"Male","mobile_no_search":"***********","registered_address":"test","registered_governorate":"Cairo","registered_division":"الشرقية","registered_city":"النزهة","current_address":"test","current_governorate":"Cairo","current_division":"الشرقية","current_city":"النزهة","loan_monthly_installment":0,"creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null,"idx":null}', 'method': 'extract_nid_data', 'cmd': 'run_doc_method'}
2025-02-23 16:51:05,111 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doc': '{"docstatus":0,"doctype":"Beneficiary","name":"new-beneficiary-bqatjmpbha","__islocal":1,"__unsaved":1,"owner":"Administrator","mobile_nos":[{"docstatus":0,"doctype":"Beneficiary Mobile Number","name":"new-beneficiary-mobile-number-hjjrfvzldd","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-beneficiary-bqatjmpbha","parentfield":"mobile_nos","parenttype":"Beneficiary","idx":1,"mobile_no":"01117549322","creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null}],"as_registered_address":"Yes","marital_status":"Single","educational_qualification":"Higher Education","handicapped":"No","disability":[],"family_member_handicapped":"No","family_member_disability":[],"pm_holder":"No","previous_loans":"No","loan_amount":0,"incomes":[{"docstatus":0,"doctype":"Income Details","name":"new-income-details-kowadsrdsk","__islocal":1,"__unsaved":1,"owner":"Administrator","type":"مرتب","frequency":"Monthly","parent":"new-beneficiary-bqatjmpbha","parentfield":"incomes","parenttype":"Beneficiary","idx":1,"amount":10000,"creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null}],"has_meeza_card":"No","has_bank_account":"No","__run_link_triggers":false,"full_name":"testage1","nid":"**************","dob":"********","age":null,"gender":"Female","mobile_no_search":"***********","registered_address":"test","registered_governorate":"Cairo","registered_division":"الشرقية","registered_city":"النزهة","current_address":"test","current_governorate":"Cairo","current_division":"الشرقية","current_city":"النزهة","loan_monthly_installment":0,"creation":"","modified_by":"Administrator","modified":"","lft":null,"rgt":null,"idx":null}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-02-24 17:11:03,975 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'reference_doctype': 'Beneficiary', 'reference_name': '**************', 'content': '<div class="ql-editor read-mode"><p>xxx</p></div>', 'comment_email': 'Administrator', 'comment_by': 'Administrator', 'cmd': 'frappe.desk.form.utils.add_comment'}
2025-02-25 14:52:27,401 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doc': '{"docstatus":0,"doctype":"Loan Bulk Disbursed","name":"new-loan-bulk-disbursed-kjazvecnbk","__islocal":1,"__unsaved":1,"owner":"Administrator","loans":[{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-medfdfprng","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-kjazvecnbk","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":1,"loan":["ACC-LOAN-2025-00001","test"]},{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-hiidqnpywl","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-kjazvecnbk","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":2,"loan":["ACC-LOAN-2025-00002","test"]},{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-jpleismxcf","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-kjazvecnbk","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":3,"loan":["ACC-LOAN-2025-00001","test"]},{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-rfjwbnatjz","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-kjazvecnbk","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":4,"loan":["ACC-LOAN-2025-00002","test"]}],"naming_series":"L-BD-.YYYY.-.#","loan_program":"التدخل الميداني - اتفاقية 3","loan_disbursement_report":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-02-25 14:53:12,987 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doc': '{"docstatus":0,"doctype":"Loan Bulk Disbursed","name":"new-loan-bulk-disbursed-kjazvecnbk","__islocal":1,"__unsaved":1,"owner":"Administrator","loans":[{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-medfdfprng","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-kjazvecnbk","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":1,"loan":["ACC-LOAN-2025-00001","test"]},{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-hiidqnpywl","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-kjazvecnbk","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":2,"loan":["ACC-LOAN-2025-00002","test"]}],"naming_series":"L-BD-.YYYY.-.#","loan_program":"التدخل الميداني - اتفاقية 3","loan_disbursement_report":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-02-25 14:54:21,152 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doc': '{"docstatus":0,"doctype":"Loan Bulk Disbursed","name":"new-loan-bulk-disbursed-kjazvecnbk","__islocal":1,"__unsaved":1,"owner":"Administrator","loans":[{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-medfdfprng","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-kjazvecnbk","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":1,"loan":["ACC-LOAN-2025-00001","test"]},{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-hiidqnpywl","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-kjazvecnbk","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":2,"loan":["ACC-LOAN-2025-00002","test"]}],"naming_series":"L-BD-.YYYY.-.#","loan_program":"التدخل الميداني - اتفاقية 3","loan_disbursement_report":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-02-25 15:02:27,397 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doc': '{"docstatus":0,"doctype":"Loan Bulk Disbursed","name":"new-loan-bulk-disbursed-qwolohfenz","__islocal":1,"__unsaved":1,"owner":"Administrator","loans":[{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-upezrdaeco","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-qwolohfenz","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":1,"loan":["ACC-LOAN-2025-00001","test"]},{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-lwyqsicozb","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-qwolohfenz","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":2,"loan":["ACC-LOAN-2025-00002","test"]}],"naming_series":"L-BD-.YYYY.-.#","loan_program":"التدخل الميداني - اتفاقية 3","loan_disbursement_report":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-02-25 15:14:39,552 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doc': '{"docstatus":0,"doctype":"Loan Bulk Disbursed","name":"new-loan-bulk-disbursed-oyljyukcij","__islocal":1,"__unsaved":1,"owner":"Administrator","loans":[{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-gooeknhmvk","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-oyljyukcij","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":1,"loan":["ACC-LOAN-2025-00001","test"]},{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-sqqxdupxgj","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-oyljyukcij","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":2,"loan":["ACC-LOAN-2025-00002","test"]}],"naming_series":"L-BD-.YYYY.-.#","loan_program":"التدخل الميداني - اتفاقية 3","loan_disbursement_report":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-02-25 15:22:47,118 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'doc': '{"docstatus":0,"doctype":"Loan Bulk Disbursed","name":"new-loan-bulk-disbursed-wnexrpuxkf","__islocal":1,"__unsaved":1,"owner":"Administrator","loans":[{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-nbcnuefrzu","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-wnexrpuxkf","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":1,"loan":["ACC-LOAN-2025-00001","test"]},{"docstatus":0,"doctype":"Disbursable Loan","name":"new-disbursable-loan-aifbdcogtv","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-bulk-disbursed-wnexrpuxkf","parentfield":"loans","parenttype":"Loan Bulk Disbursed","idx":2,"loan":["ACC-LOAN-2025-00002","test"]}],"naming_series":"L-BD-.YYYY.-.#","loan_program":"التدخل الميداني - اتفاقية 3","loan_disbursement_report":"/private/files/loan-request-new-loan-request-xoukxdonde-2740c.png"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-03-02 15:13:46,464 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'docs': '{"docstatus":0,"doctype":"Loan Request","name":"new-loan-request-azbxurrcxd","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"المؤسسة العامة للتكافل الإجتماعي","naming_series":"LR-.YYYY.-.#","status":"Draft","creation_date":"2025-03-02","is_web_form":0,"mobile_nos":[{"docstatus":0,"doctype":"Loan Request Mobile Number","name":"new-loan-request-mobile-number-uawhfpnpph","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-request-azbxurrcxd","parentfield":"mobile_nos","parenttype":"Loan Request","idx":1}],"as_registered_address":"No","marital_status":"","educational_qualification":"","handicapped":"","disability":[],"family_member_handicapped":"","family_member_disability":[],"pm_holder":"","previous_loans":"","loan_amount":0,"incomes":[{"docstatus":0,"doctype":"Income Details","name":"new-income-details-qdraozrxhd","__islocal":1,"__unsaved":1,"owner":"Administrator","type":"","frequency":"","parent":"new-loan-request-azbxurrcxd","parentfield":"incomes","parenttype":"Loan Request","idx":1}],"has_meeza_card":"No","has_bank_account":"No","project_status":"","application_fees_type":"Amount","insurance_fees_type":"Amount","late_payment_fees_type":"Amount","guarantor_mobile_nos":[{"docstatus":0,"doctype":"Loan Request Mobile Number","name":"new-loan-request-mobile-number-yrytljawsi","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-request-azbxurrcxd","parentfield":"guarantor_mobile_nos","parenttype":"Loan Request","idx":1}],"guarantor_as_registered_address":"No","attachments_template":"Basic Loan Request","attachments":[{"docstatus":0,"doctype":"Attachment","name":"new-attachment-dparxtnssk","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-azbxurrcxd","parentfield":"attachments","parenttype":"Loan Request","idx":1,"type":"Beneficiary National ID Front","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-sipppaafsm","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-azbxurrcxd","parentfield":"attachments","parenttype":"Loan Request","idx":2,"type":"Beneficiary National ID Back","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-tpidtjkylw","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-azbxurrcxd","parentfield":"attachments","parenttype":"Loan Request","idx":3,"type":"Guarantor National ID Front","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-nznmwyubvb","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-azbxurrcxd","parentfield":"attachments","parenttype":"Loan Request","idx":4,"type":"Guarantor National ID Back","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-umbinswzgx","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-azbxurrcxd","parentfield":"attachments","parenttype":"Loan Request","idx":5,"type":"Beneficiary Utility Bill","linked_to":"Beneficiary"}],"feasibility_studies":[],"social_investigations":[],"iscore_status":"","extra_documents":[],"nid":"26606151301442"}', 'method': 'validate_beneficiary_age', 'cmd': 'run_doc_method'}
2025-03-02 15:17:53,992 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'docs': '{"docstatus":0,"doctype":"Loan Request","name":"new-loan-request-hvqjycrqwg","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"المؤسسة العامة للتكافل الإجتماعي","naming_series":"LR-.YYYY.-.#","status":"Draft","creation_date":"2025-03-02","is_web_form":0,"mobile_nos":[],"disability":[],"family_member_disability":[],"loan_amount":0,"incomes":[],"project_status":"","application_fees_type":"Amount","insurance_fees_type":"Amount","late_payment_fees_type":"Amount","guarantor_mobile_nos":[{"docstatus":0,"doctype":"Loan Request Mobile Number","name":"new-loan-request-mobile-number-ujtpcrjthx","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-request-hvqjycrqwg","parentfield":"guarantor_mobile_nos","parenttype":"Loan Request","idx":1}],"guarantor_as_registered_address":"No","attachments_template":"Basic Loan Request","attachments":[{"docstatus":0,"doctype":"Attachment","name":"new-attachment-pimwxegjle","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":1,"type":"Beneficiary National ID Front","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-qhwiunvebe","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":2,"type":"Beneficiary National ID Back","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yvhcfboczu","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":3,"type":"Guarantor National ID Front","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yeidumhrdi","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":4,"type":"Guarantor National ID Back","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-dbqwrzcwkx","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":5,"type":"Beneficiary Utility Bill","linked_to":"Beneficiary"}],"feasibility_studies":[],"social_investigations":[],"iscore_status":"","extra_documents":[],"beneficiary":"","idx":0,"age":0,"loan_monthly_installment":0,"lp_amount":0,"payment_window":0,"grace_period":0,"number_of_installments":0,"interest_rate":0,"application_fees_amount":0,"application_fees_percentage":0,"insurance_fees_amount":0,"insurance_fees_percentage":0,"late_payment_fees_amount":0,"late_payment_fees_percentage":0,"early_payment_fees_percentage":0,"repayment_total":0,"monthly_installment":0,"application_fees_loan_amount":0,"interest_total":0,"last_monthly_installment":0,"insurance_fees_loan_amount":0,"iscore_result":0,"iscore_loans":0,"iscore_total_balance":0}', 'method': 'validate_beneficiary_age', 'cmd': 'run_doc_method'}
2025-03-02 15:17:59,597 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'docs': '{"docstatus":0,"doctype":"Loan Request","name":"new-loan-request-hvqjycrqwg","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"المؤسسة العامة للتكافل الإجتماعي","naming_series":"LR-.YYYY.-.#","status":"Draft","creation_date":"2025-03-02","is_web_form":0,"mobile_nos":[],"disability":[],"family_member_disability":[],"loan_amount":0,"incomes":[],"project_status":"","application_fees_type":"Amount","insurance_fees_type":"Amount","late_payment_fees_type":"Amount","guarantor_mobile_nos":[{"docstatus":0,"doctype":"Loan Request Mobile Number","name":"new-loan-request-mobile-number-ujtpcrjthx","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-request-hvqjycrqwg","parentfield":"guarantor_mobile_nos","parenttype":"Loan Request","idx":1}],"guarantor_as_registered_address":"No","attachments_template":"Basic Loan Request","attachments":[{"docstatus":0,"doctype":"Attachment","name":"new-attachment-pimwxegjle","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":1,"type":"Beneficiary National ID Front","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-qhwiunvebe","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":2,"type":"Beneficiary National ID Back","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yvhcfboczu","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":3,"type":"Guarantor National ID Front","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yeidumhrdi","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":4,"type":"Guarantor National ID Back","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-dbqwrzcwkx","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":5,"type":"Beneficiary Utility Bill","linked_to":"Beneficiary"}],"feasibility_studies":[],"social_investigations":[],"iscore_status":"","extra_documents":[],"beneficiary":"","idx":0,"age":0,"loan_monthly_installment":0,"lp_amount":0,"payment_window":0,"grace_period":0,"number_of_installments":0,"interest_rate":0,"application_fees_amount":0,"application_fees_percentage":0,"insurance_fees_amount":0,"insurance_fees_percentage":0,"late_payment_fees_amount":0,"late_payment_fees_percentage":0,"early_payment_fees_percentage":0,"repayment_total":0,"monthly_installment":0,"application_fees_loan_amount":0,"interest_total":0,"last_monthly_installment":0,"insurance_fees_loan_amount":0,"iscore_result":0,"iscore_loans":0,"iscore_total_balance":0}', 'method': 'validate_beneficiary_age', 'cmd': 'run_doc_method'}
2025-03-02 15:18:04,830 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'docs': '{"docstatus":0,"doctype":"Loan Request","name":"new-loan-request-hvqjycrqwg","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"المؤسسة العامة للتكافل الإجتماعي","naming_series":"LR-.YYYY.-.#","status":"Draft","creation_date":"2025-03-02","is_web_form":0,"mobile_nos":[],"disability":[],"family_member_disability":[],"loan_amount":0,"incomes":[],"project_status":"","application_fees_type":"Amount","insurance_fees_type":"Amount","late_payment_fees_type":"Amount","guarantor_mobile_nos":[{"docstatus":0,"doctype":"Loan Request Mobile Number","name":"new-loan-request-mobile-number-ujtpcrjthx","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-request-hvqjycrqwg","parentfield":"guarantor_mobile_nos","parenttype":"Loan Request","idx":1}],"guarantor_as_registered_address":"No","attachments_template":"Basic Loan Request","attachments":[{"docstatus":0,"doctype":"Attachment","name":"new-attachment-pimwxegjle","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":1,"type":"Beneficiary National ID Front","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-qhwiunvebe","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":2,"type":"Beneficiary National ID Back","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yvhcfboczu","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":3,"type":"Guarantor National ID Front","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yeidumhrdi","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":4,"type":"Guarantor National ID Back","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-dbqwrzcwkx","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":5,"type":"Beneficiary Utility Bill","linked_to":"Beneficiary"}],"feasibility_studies":[],"social_investigations":[],"iscore_status":"","extra_documents":[],"beneficiary":"","idx":0,"age":0,"loan_monthly_installment":0,"lp_amount":0,"payment_window":0,"grace_period":0,"number_of_installments":0,"interest_rate":0,"application_fees_amount":0,"application_fees_percentage":0,"insurance_fees_amount":0,"insurance_fees_percentage":0,"late_payment_fees_amount":0,"late_payment_fees_percentage":0,"early_payment_fees_percentage":0,"repayment_total":0,"monthly_installment":0,"application_fees_loan_amount":0,"interest_total":0,"last_monthly_installment":0,"insurance_fees_loan_amount":0,"iscore_result":0,"iscore_loans":0,"iscore_total_balance":0}', 'method': 'validate_beneficiary_age', 'cmd': 'run_doc_method'}
2025-03-02 15:18:14,589 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'docs': '{"docstatus":0,"doctype":"Loan Request","name":"new-loan-request-hvqjycrqwg","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"المؤسسة العامة للتكافل الإجتماعي","naming_series":"LR-.YYYY.-.#","status":"Draft","creation_date":"2025-03-02","is_web_form":0,"mobile_nos":[],"disability":[],"family_member_disability":[],"loan_amount":0,"incomes":[],"project_status":"","application_fees_type":"Amount","insurance_fees_type":"Amount","late_payment_fees_type":"Amount","guarantor_mobile_nos":[{"docstatus":0,"doctype":"Loan Request Mobile Number","name":"new-loan-request-mobile-number-ujtpcrjthx","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-request-hvqjycrqwg","parentfield":"guarantor_mobile_nos","parenttype":"Loan Request","idx":1}],"guarantor_as_registered_address":"No","attachments_template":"Basic Loan Request","attachments":[{"docstatus":0,"doctype":"Attachment","name":"new-attachment-pimwxegjle","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":1,"type":"Beneficiary National ID Front","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-qhwiunvebe","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":2,"type":"Beneficiary National ID Back","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yvhcfboczu","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":3,"type":"Guarantor National ID Front","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yeidumhrdi","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":4,"type":"Guarantor National ID Back","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-dbqwrzcwkx","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":5,"type":"Beneficiary Utility Bill","linked_to":"Beneficiary"}],"feasibility_studies":[],"social_investigations":[],"iscore_status":"","extra_documents":[],"beneficiary":"","idx":0,"age":0,"loan_monthly_installment":0,"lp_amount":0,"payment_window":0,"grace_period":0,"number_of_installments":0,"interest_rate":0,"application_fees_amount":0,"application_fees_percentage":0,"insurance_fees_amount":0,"insurance_fees_percentage":0,"late_payment_fees_amount":0,"late_payment_fees_percentage":0,"early_payment_fees_percentage":0,"repayment_total":0,"monthly_installment":0,"application_fees_loan_amount":0,"interest_total":0,"last_monthly_installment":0,"insurance_fees_loan_amount":0,"iscore_result":0,"iscore_loans":0,"iscore_total_balance":0}', 'method': 'validate_beneficiary_age', 'cmd': 'run_doc_method'}
2025-03-02 15:18:19,425 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'docs': '{"docstatus":0,"doctype":"Loan Request","name":"new-loan-request-hvqjycrqwg","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"المؤسسة العامة للتكافل الإجتماعي","naming_series":"LR-.YYYY.-.#","status":"Draft","creation_date":"2025-03-02","is_web_form":0,"mobile_nos":[],"disability":[],"family_member_disability":[],"loan_amount":0,"incomes":[],"project_status":"","application_fees_type":"Amount","insurance_fees_type":"Amount","late_payment_fees_type":"Amount","guarantor_mobile_nos":[{"docstatus":0,"doctype":"Loan Request Mobile Number","name":"new-loan-request-mobile-number-ujtpcrjthx","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-request-hvqjycrqwg","parentfield":"guarantor_mobile_nos","parenttype":"Loan Request","idx":1}],"guarantor_as_registered_address":"No","attachments_template":"Basic Loan Request","attachments":[{"docstatus":0,"doctype":"Attachment","name":"new-attachment-pimwxegjle","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":1,"type":"Beneficiary National ID Front","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-qhwiunvebe","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":2,"type":"Beneficiary National ID Back","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yvhcfboczu","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":3,"type":"Guarantor National ID Front","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yeidumhrdi","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":4,"type":"Guarantor National ID Back","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-dbqwrzcwkx","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":5,"type":"Beneficiary Utility Bill","linked_to":"Beneficiary"}],"feasibility_studies":[],"social_investigations":[],"iscore_status":"","extra_documents":[],"beneficiary":"","idx":0,"age":0,"loan_monthly_installment":0,"lp_amount":0,"payment_window":0,"grace_period":0,"number_of_installments":0,"interest_rate":0,"application_fees_amount":0,"application_fees_percentage":0,"insurance_fees_amount":0,"insurance_fees_percentage":0,"late_payment_fees_amount":0,"late_payment_fees_percentage":0,"early_payment_fees_percentage":0,"repayment_total":0,"monthly_installment":0,"application_fees_loan_amount":0,"interest_total":0,"last_monthly_installment":0,"insurance_fees_loan_amount":0,"iscore_result":0,"iscore_loans":0,"iscore_total_balance":0}', 'method': 'validate_beneficiary_age', 'cmd': 'run_doc_method'}
2025-03-02 15:18:53,336 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'docs': '{"docstatus":0,"doctype":"Loan Request","name":"new-loan-request-hvqjycrqwg","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"المؤسسة العامة للتكافل الإجتماعي","naming_series":"LR-.YYYY.-.#","status":"Draft","creation_date":"2025-03-02","is_web_form":0,"mobile_nos":[],"disability":[],"family_member_disability":[],"loan_amount":0,"incomes":[],"project_status":"","application_fees_type":"Amount","insurance_fees_type":"Amount","late_payment_fees_type":"Amount","guarantor_mobile_nos":[{"docstatus":0,"doctype":"Loan Request Mobile Number","name":"new-loan-request-mobile-number-ujtpcrjthx","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-request-hvqjycrqwg","parentfield":"guarantor_mobile_nos","parenttype":"Loan Request","idx":1}],"guarantor_as_registered_address":"No","attachments_template":"Basic Loan Request","attachments":[{"docstatus":0,"doctype":"Attachment","name":"new-attachment-pimwxegjle","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":1,"type":"Beneficiary National ID Front","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-qhwiunvebe","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":2,"type":"Beneficiary National ID Back","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yvhcfboczu","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":3,"type":"Guarantor National ID Front","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-yeidumhrdi","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":4,"type":"Guarantor National ID Back","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-dbqwrzcwkx","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-hvqjycrqwg","parentfield":"attachments","parenttype":"Loan Request","idx":5,"type":"Beneficiary Utility Bill","linked_to":"Beneficiary"}],"feasibility_studies":[],"social_investigations":[],"iscore_status":"","extra_documents":[],"beneficiary":"","idx":0,"age":0,"loan_monthly_installment":0,"lp_amount":0,"payment_window":0,"grace_period":0,"number_of_installments":0,"interest_rate":0,"application_fees_amount":0,"application_fees_percentage":0,"insurance_fees_amount":0,"insurance_fees_percentage":0,"late_payment_fees_amount":0,"late_payment_fees_percentage":0,"early_payment_fees_percentage":0,"repayment_total":0,"monthly_installment":0,"application_fees_loan_amount":0,"interest_total":0,"last_monthly_installment":0,"insurance_fees_loan_amount":0,"iscore_result":0,"iscore_loans":0,"iscore_total_balance":0,"nid":"26605151301442"}', 'method': 'validate_beneficiary_age', 'cmd': 'run_doc_method'}
2025-03-02 15:20:42,732 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'docs': '{"docstatus":0,"doctype":"Loan Request","name":"new-loan-request-ftxzbfnhjy","__islocal":1,"__unsaved":1,"owner":"Administrator","company":"المؤسسة العامة للتكافل الإجتماعي","naming_series":"LR-.YYYY.-.#","status":"Draft","creation_date":"2025-03-02","is_web_form":0,"mobile_nos":[{"docstatus":0,"doctype":"Loan Request Mobile Number","name":"new-loan-request-mobile-number-mfixmeenvr","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-request-ftxzbfnhjy","parentfield":"mobile_nos","parenttype":"Loan Request","idx":1}],"as_registered_address":"No","marital_status":"","educational_qualification":"","handicapped":"","disability":[],"family_member_handicapped":"","family_member_disability":[],"pm_holder":"","previous_loans":"","loan_amount":0,"incomes":[{"docstatus":0,"doctype":"Income Details","name":"new-income-details-otjgibxjms","__islocal":1,"__unsaved":1,"owner":"Administrator","type":"","frequency":"","parent":"new-loan-request-ftxzbfnhjy","parentfield":"incomes","parenttype":"Loan Request","idx":1}],"has_meeza_card":"No","has_bank_account":"No","project_status":"","application_fees_type":"Amount","insurance_fees_type":"Amount","late_payment_fees_type":"Amount","guarantor_mobile_nos":[{"docstatus":0,"doctype":"Loan Request Mobile Number","name":"new-loan-request-mobile-number-pwfnbumsxs","__islocal":1,"__unsaved":1,"owner":"Administrator","parent":"new-loan-request-ftxzbfnhjy","parentfield":"guarantor_mobile_nos","parenttype":"Loan Request","idx":1}],"guarantor_as_registered_address":"No","attachments_template":"Basic Loan Request","attachments":[{"docstatus":0,"doctype":"Attachment","name":"new-attachment-dawcozfipf","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-ftxzbfnhjy","parentfield":"attachments","parenttype":"Loan Request","idx":1,"type":"Beneficiary National ID Front","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-odeztjbxxy","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-ftxzbfnhjy","parentfield":"attachments","parenttype":"Loan Request","idx":2,"type":"Beneficiary National ID Back","linked_to":"Beneficiary"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-hlbhrelblq","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-ftxzbfnhjy","parentfield":"attachments","parenttype":"Loan Request","idx":3,"type":"Guarantor National ID Front","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-cmexmeyudm","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-ftxzbfnhjy","parentfield":"attachments","parenttype":"Loan Request","idx":4,"type":"Guarantor National ID Back","linked_to":"Guarantor"},{"docstatus":0,"doctype":"Attachment","name":"new-attachment-ndgcanukym","__islocal":1,"__unsaved":1,"owner":"Administrator","has_expiry_date":"Yes","parent":"new-loan-request-ftxzbfnhjy","parentfield":"attachments","parenttype":"Loan Request","idx":5,"type":"Beneficiary Utility Bill","linked_to":"Beneficiary"}],"feasibility_studies":[],"social_investigations":[],"iscore_status":"","extra_documents":[],"nid":"26106121501442"}', 'method': 'validate_beneficiary_age', 'cmd': 'run_doc_method'}
2025-03-06 13:59:21,419 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'report_name': 'Custom Balance Sheet', 'filters': '{"company":"المؤسسة العامة للتكافل الإجتماعي","filter_based_on":"Fiscal Year","period_start_date":"2025-01-01","period_end_date":"2025-12-31","from_fiscal_year":"2025","to_fiscal_year":"2025","periodicity":"Yearly","cost_center":[],"project":[],"selected_view":"Report","accumulated_values":1,"include_default_book_entries":1}', 'ignore_prepared_report': 'false', 'is_tree': 'true', 'parent_field': 'parent_account', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-03-06 14:00:00,247 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'report_name': 'Custom Balance Sheet', 'filters': '{"company":"المؤسسة العامة للتكافل الإجتماعي","filter_based_on":"Fiscal Year","period_start_date":"2025-01-01","period_end_date":"2025-12-31","from_fiscal_year":"2025","to_fiscal_year":"2025","periodicity":"Yearly","cost_center":[],"project":[],"selected_view":"Report","accumulated_values":1,"include_default_book_entries":1}', 'ignore_prepared_report': 'false', 'is_tree': 'true', 'parent_field': 'parent_account', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-03-09 05:14:03,015 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'report_name': 'Custom Balance Sheet', 'filters': '{"company":"المؤسسة العامة للتكافل الإجتماعي","filter_based_on":"Fiscal Year","period_start_date":"2025-01-01","period_end_date":"2025-12-31","from_fiscal_year":"2025","to_fiscal_year":"2025","periodicity":"Yearly","cost_center":[],"project":[],"selected_view":"Report","accumulated_values":1,"include_default_book_entries":1}', 'ignore_prepared_report': 'false', 'is_tree': 'true', 'parent_field': 'parent_account', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-03-09 05:15:18,992 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'report_name': 'Custom Balance Sheet', 'filters': '{"company":"المؤسسة العامة للتكافل الإجتماعي","filter_based_on":"Fiscal Year","period_start_date":"2025-01-01","period_end_date":"2025-12-31","from_fiscal_year":"2025","to_fiscal_year":"2025","periodicity":"Yearly","cost_center":[],"project":[],"selected_view":"Report","accumulated_values":1,"include_default_book_entries":1}', 'ignore_prepared_report': 'false', 'is_tree': 'true', 'parent_field': 'parent_account', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-03-09 05:35:21,730 ERROR frappe New Exception collected in error log
Site: child_ngo
Form Dict: {'report_name': 'Custom Balance Sheet', 'filters': '{"company":"المؤسسة العامة للتكافل الإجتماعي","filter_based_on":"Fiscal Year","period_start_date":"2025-01-01","period_end_date":"2025-12-31","from_fiscal_year":"2025","to_fiscal_year":"2025","periodicity":"Yearly","cost_center":[],"project":[],"selected_view":"Report","accumulated_values":1,"include_default_book_entries":1}', 'ignore_prepared_report': 'false', 'is_tree': 'true', 'parent_field': 'parent_account', 'are_default_filters': 'false', 'cmd': 'frappe.desk.query_report.run'}
2025-03-09 13:07:24,486 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-03-09 13:07:27,282 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-03-09 13:07:28,063 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-03-09 13:07:28,808 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-03-09 13:07:29,723 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-03-09 13:07:30,567 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-03-09 13:07:31,185 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-03-09 13:07:31,934 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-03-09 13:07:32,732 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-03-09 13:07:33,355 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-03-09 13:07:33,901 ERROR frappe Failed to run after request hook
Site: child_ngo
Form Dict: {'cmd': 'login', 'usr': '<EMAIL>', 'pwd': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
