2025-06-04 15:56:41,610 ERROR frappe Failed to run after request hook
Site: brainwise.helpdesk
Form Dict: {'limit': '1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-04 15:56:41,611 ERROR frappe Failed to run after request hook
Site: brainwise.helpdesk
Form Dict: {'limit': '1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-04 15:56:42,619 ERROR frappe Failed to run after request hook
Site: brainwise.helpdesk
Form Dict: {'limit': '1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-04 15:56:42,620 ERROR frappe Failed to run after request hook
Site: brainwise.helpdesk
Form Dict: {'limit': '1'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-06-04 17:48:27,334 ERROR frappe New Exception collected in error log
Site: brainwise.helpdesk
Form Dict: {'doc': {'description': '<p>xxxxxxxx</p>', 'subject': 'xxxxxxxxxxxxxxx', 'template': '', 'ticket_type': 'Bug', 'priority': 'High', 'doctype': 'HD Ticket', 'via_customer_portal': True, 'attachments': [{'name': '5819b80a2c', 'owner': '<EMAIL>', 'creation': '2025-06-04 17:48:20.319062', 'modified': '2025-06-04 17:48:20.319062', 'modified_by': '<EMAIL>', 'docstatus': 0, 'idx': 0, 'file_name': '2025-04-14-123121.jpg', 'is_private': 1, 'file_type': 'JPG', 'is_home_folder': 0, 'is_attachments_folder': 0, 'file_size': 148067, 'file_url': '/private/files/user-administrator-ff948.jpg', 'folder': 'Home/Helpdesk', 'is_folder': 0, 'sort_order': 0, 'from_api': 0, 'content_hash': '3c7865d1471cabb4ddddb0ddfe24939e', 'uploaded_to_dropbox': 0, 'uploaded_to_google_drive': 0, 'doctype': 'File'}]}, 'attachments': [{'name': '5819b80a2c', 'owner': '<EMAIL>', 'creation': '2025-06-04 17:48:20.319062', 'modified': '2025-06-04 17:48:20.319062', 'modified_by': '<EMAIL>', 'docstatus': 0, 'idx': 0, 'file_name': '2025-04-14-123121.jpg', 'is_private': 1, 'file_type': 'JPG', 'is_home_folder': 0, 'is_attachments_folder': 0, 'file_size': 148067, 'file_url': '/private/files/user-administrator-ff948.jpg', 'folder': 'Home/Helpdesk', 'is_folder': 0, 'sort_order': 0, 'from_api': 0, 'content_hash': '3c7865d1471cabb4ddddb0ddfe24939e', 'uploaded_to_dropbox': 0, 'uploaded_to_google_drive': 0, 'doctype': 'File'}], 'cmd': 'helpdesk.helpdesk.doctype.hd_ticket.api.new'}
