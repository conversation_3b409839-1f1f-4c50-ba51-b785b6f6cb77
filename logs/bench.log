2025-01-27 17:23:00,392 DEBUG cd frappe-bench && python3 -m venv env
2025-01-27 17:23:03,148 DEBUG cd frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade pip
2025-01-27 17:23:05,157 DEBUG cd frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet wheel
2025-01-27 17:23:12,254 LOG Getting frappe
2025-01-27 17:23:12,255 DEBUG cd frappe-bench/apps && git clone https://github.com/frappe/frappe.git --branch version-15 --depth 1 --origin upstream
2025-01-27 17:23:55,048 LOG Installing frappe
2025-01-27 17:23:55,048 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/frappe 
2025-01-27 17:30:45,444 DEBUG cd /home/<USER>/frappe-bench/apps/frappe && yarn install --check-files
2025-01-27 17:35:04,716 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-01-27 17:35:04,716 DEBUG cd frappe-bench && bench build
2025-01-27 17:35:04,823 INFO /usr/local/bin/bench build
2025-01-27 17:35:22,813 LOG setting up backups
2025-01-27 17:35:22,829 LOG backups were set up
2025-01-27 17:35:22,829 INFO Bench frappe-bench initialized
2025-01-27 17:36:45,607 INFO /usr/local/bin/bench new-site child_ngo
2025-01-27 17:37:47,982 INFO /usr/local/bin/bench --site child_ngo enable-scheduler
2025-01-27 17:38:03,166 INFO /usr/local/bin/bench --site child_ngo set-maintenance-mode off
2025-01-27 17:38:18,799 INFO /usr/local/bin/bench --site child_ngo set-config server_script_enabled true
2025-01-27 17:38:28,727 INFO /usr/local/bin/bench set-config -g developer_mode true
2025-01-27 17:47:02,164 INFO /usr/local/bin/bench --site child_ngo add-to-hosts
2025-01-27 17:48:07,151 INFO /usr/local/bin/bench get-app payments --branch version-15
2025-01-27 17:48:07,685 LOG Getting payments
2025-01-27 17:48:07,686 DEBUG cd ./apps && git clone https://github.com/frappe/payments.git --branch version-15 --depth 1 --origin upstream
2025-01-27 17:48:08,674 LOG Installing payments
2025-01-27 17:48:08,674 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/payments 
2025-01-27 17:48:14,598 DEBUG bench build --app payments
2025-01-27 17:48:14,705 INFO /usr/local/bin/bench build --app payments
2025-01-27 17:48:16,102 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-01-27 17:48:19,729 INFO /usr/local/bin/bench get-app erpnext --branch version-15
2025-01-27 17:48:20,374 LOG Getting erpnext
2025-01-27 17:48:20,374 DEBUG cd ./apps && git clone https://github.com/frappe/erpnext.git --branch version-15 --depth 1 --origin upstream
2025-01-27 17:48:25,035 LOG Installing erpnext
2025-01-27 17:48:25,035 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/erpnext 
2025-01-27 17:48:40,727 DEBUG cd /home/<USER>/frappe-bench/apps/erpnext && yarn install --check-files
2025-01-27 17:48:41,657 DEBUG bench build --app erpnext
2025-01-27 17:48:41,757 INFO /usr/local/bin/bench build --app erpnext
2025-01-27 17:48:44,243 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-01-27 17:48:59,943 INFO /usr/local/bin/bench get-app --branch <NAME_EMAIL>:BrainWise-DEV/IAM.git
2025-01-27 17:48:59,950 LOG Getting IAM
2025-01-27 17:48:59,950 DEBUG cd ./apps && <NAME_EMAIL>:BrainWise-DEV/IAM.git --branch dev --depth 1 --origin upstream
2025-01-27 17:49:07,764 WARNING cd ./apps && <NAME_EMAIL>:BrainWise-DEV/IAM.git --branch dev --depth 1 --origin upstream executed with exit code 128
2025-01-27 17:49:07,765 WARNING /usr/local/bin/bench get-app --branch <NAME_EMAIL>:BrainWise-DEV/IAM.git executed with exit code 1
2025-01-27 17:52:30,087 INFO /usr/local/bin/bench get-app --branch <NAME_EMAIL>:BrainWise-DEV/IAM.git
2025-01-27 17:52:30,093 LOG Getting IAM
2025-01-27 17:52:30,093 DEBUG cd ./apps && <NAME_EMAIL>:BrainWise-DEV/IAM.git --branch dev --depth 1 --origin upstream
2025-01-27 17:52:31,324 WARNING cd ./apps && <NAME_EMAIL>:BrainWise-DEV/IAM.git --branch dev --depth 1 --origin upstream executed with exit code 128
2025-01-27 17:52:31,324 WARNING /usr/local/bin/bench get-app --branch <NAME_EMAIL>:BrainWise-DEV/IAM.git executed with exit code 1
2025-01-27 17:53:23,281 INFO /usr/local/bin/bench get-app --branch <NAME_EMAIL>:BrainWise-DEV/IAM.git
2025-01-27 17:53:23,287 LOG Getting IAM
2025-01-27 17:53:23,287 DEBUG cd ./apps && <NAME_EMAIL>:BrainWise-DEV/IAM.git --branch dev --depth 1 --origin upstream
2025-01-27 17:53:24,300 WARNING cd ./apps && <NAME_EMAIL>:BrainWise-DEV/IAM.git --branch dev --depth 1 --origin upstream executed with exit code 128
2025-01-27 17:53:24,301 WARNING /usr/local/bin/bench get-app --branch <NAME_EMAIL>:BrainWise-DEV/IAM.git executed with exit code 1
2025-01-27 17:55:04,719 INFO /usr/local/bin/bench get-app --branch <NAME_EMAIL>:BrainWise-DEV/IAM.git
2025-01-27 17:55:04,725 LOG Getting IAM
2025-01-27 17:55:04,725 DEBUG cd ./apps && <NAME_EMAIL>:BrainWise-DEV/IAM.git --branch dev --depth 1 --origin upstream
2025-01-27 17:55:06,548 WARNING cd ./apps && <NAME_EMAIL>:BrainWise-DEV/IAM.git --branch dev --depth 1 --origin upstream executed with exit code 128
2025-01-27 17:55:06,548 WARNING /usr/local/bin/bench get-app --branch <NAME_EMAIL>:BrainWise-DEV/IAM.git executed with exit code 1
2025-01-27 18:00:01,942 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-27 18:02:00,422 INFO /usr/local/bin/bench get-app --branch <NAME_EMAIL>:BrainWise-DEV/IAM.git
2025-01-27 18:02:00,428 LOG Getting IAM
2025-01-27 18:02:00,428 DEBUG cd ./apps && <NAME_EMAIL>:BrainWise-DEV/IAM.git --branch dev --depth 1 --origin upstream
2025-01-27 18:02:14,201 LOG Installing iam
2025-01-27 18:02:14,201 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/iam 
2025-01-27 18:02:16,084 DEBUG bench build --app iam
2025-01-27 18:02:16,190 INFO /usr/local/bin/bench build --app iam
2025-01-27 18:02:17,831 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-01-27 18:06:15,384 INFO /usr/local/bin/bench get-app --branch dev license_manager_client **************:BrainWise-DEV/license_manager_client.git
2025-01-27 18:06:15,391 LOG Getting license_manager_client
2025-01-27 18:06:15,391 DEBUG cd ./apps && <NAME_EMAIL>:BrainWise-DEV/license_manager_client.git --branch dev --depth 1 --origin upstream
2025-01-27 18:06:19,514 LOG Installing license_manager_client
2025-01-27 18:06:19,515 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/license_manager_client 
2025-01-27 18:06:21,781 DEBUG bench build --app license_manager_client
2025-01-27 18:06:21,905 INFO /usr/local/bin/bench build --app license_manager_client
2025-01-27 18:06:23,731 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-01-27 18:06:28,203 INFO /usr/local/bin/bench get-app --branch <NAME_EMAIL>:BrainWise-DEV/ISCORE.git
2025-01-27 18:06:28,211 LOG Getting ISCORE
2025-01-27 18:06:28,211 DEBUG cd ./apps && <NAME_EMAIL>:BrainWise-DEV/ISCORE.git --branch dev --depth 1 --origin upstream
2025-01-27 18:06:30,581 LOG Installing iscore
2025-01-27 18:06:30,582 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/iscore 
2025-01-27 18:06:32,827 DEBUG bench build --app iscore
2025-01-27 18:06:32,935 INFO /usr/local/bin/bench build --app iscore
2025-01-27 18:06:34,797 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-01-27 18:06:42,188 INFO /usr/local/bin/bench get-app --branch <NAME_EMAIL>:BrainWise-DEV/NGO.git
2025-01-27 18:06:42,194 LOG Getting NGO
2025-01-27 18:06:42,194 DEBUG cd ./apps && <NAME_EMAIL>:BrainWise-DEV/NGO.git --branch dev --depth 1 --origin upstream
2025-01-27 18:06:45,000 LOG Installing ngo
2025-01-27 18:06:45,001 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/ngo 
2025-01-27 18:06:47,569 DEBUG bench build --app ngo
2025-01-27 18:06:47,682 INFO /usr/local/bin/bench build --app ngo
2025-01-27 18:06:49,598 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-01-27 18:06:56,181 INFO /usr/local/bin/bench --site child_ngo install-app ngo
2025-01-27 18:09:40,733 INFO /usr/local/bin/bench start
2025-01-27 18:09:40,944 INFO /usr/local/bin/bench worker
2025-01-27 18:09:40,953 INFO /usr/local/bin/bench watch
2025-01-27 18:09:41,015 INFO /usr/local/bin/bench serve --port 8000
2025-01-27 18:09:41,020 INFO /usr/local/bin/bench schedule
2025-01-27 18:10:49,158 INFO /usr/local/bin/bench --site child_ngo restore /home/<USER>/Documents/NGO/20250113_161757-ngo-database.sql.gz --with-public-files /home/<USER>/Documents/NGO/20250113_161757-ngo-files.tar --with-private-files /home/<USER>/Documents/NGO/20250113_161757-ngo-private-files.tar
2025-01-27 18:12:19,401 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-27 18:15:25,967 INFO /usr/local/bin/bench --site child_ngo set-admin-password root
2025-01-27 18:15:48,576 INFO /usr/local/bin/bench restart
2025-01-27 18:15:48,583 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-01-27 18:15:56,089 INFO /usr/local/bin/bench start
2025-01-27 18:15:56,353 INFO /usr/local/bin/bench schedule
2025-01-27 18:15:56,409 INFO /usr/local/bin/bench serve --port 8000
2025-01-27 18:15:56,410 INFO /usr/local/bin/bench worker
2025-01-27 18:15:56,416 INFO /usr/local/bin/bench watch
2025-01-27 18:18:32,964 INFO /usr/local/bin/bench --site child_ngo set-admin-password root
2025-01-28 00:00:01,881 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-28 08:55:22,033 INFO /usr/local/bin/bench start
2025-01-28 08:55:22,250 INFO /usr/local/bin/bench schedule
2025-01-28 08:55:22,251 INFO /usr/local/bin/bench worker
2025-01-28 08:55:22,257 INFO /usr/local/bin/bench watch
2025-01-28 08:55:22,270 INFO /usr/local/bin/bench serve --port 8000
2025-01-28 12:00:01,658 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-28 15:44:07,601 INFO /usr/local/bin/bench start
2025-01-28 15:44:07,787 INFO /usr/local/bin/bench watch
2025-01-28 15:44:07,802 INFO /usr/local/bin/bench serve --port 8000
2025-01-28 15:44:07,818 INFO /usr/local/bin/bench schedule
2025-01-28 15:44:07,856 INFO /usr/local/bin/bench worker
2025-01-28 15:48:22,316 INFO /usr/local/bin/bench set-config -g developer_mode 1
2025-01-28 15:48:35,513 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-01-28 15:48:38,434 INFO /usr/local/bin/bench start
2025-01-28 15:48:38,622 INFO /usr/local/bin/bench watch
2025-01-28 15:48:38,630 INFO /usr/local/bin/bench worker
2025-01-28 15:48:38,676 INFO /usr/local/bin/bench serve --port 8000
2025-01-28 15:48:38,692 INFO /usr/local/bin/bench schedule
2025-01-28 15:51:29,915 INFO /usr/local/bin/bench set-config -g developer_mode 1
2025-01-28 15:52:36,103 INFO /usr/local/bin/bench --site child_ngo set-config enable_scheduler true
2025-01-28 15:52:56,184 INFO /usr/local/bin/bench --site child_ngo set-maintenance-mode off
2025-01-28 15:53:30,125 INFO /usr/local/bin/bench set-config -g developer_mode 1
2025-01-28 15:53:44,017 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-01-28 15:53:48,985 INFO /usr/local/bin/bench start
2025-01-28 15:53:49,177 INFO /usr/local/bin/bench schedule
2025-01-28 15:53:49,187 INFO /usr/local/bin/bench serve --port 8000
2025-01-28 15:53:49,188 INFO /usr/local/bin/bench worker
2025-01-28 15:53:49,237 INFO /usr/local/bin/bench watch
2025-01-28 18:00:01,306 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-28 21:35:25,755 INFO /usr/local/bin/bench --site child_ngo export-fixtures
2025-01-28 22:32:55,840 INFO /usr/local/bin/bench start
2025-01-28 22:32:56,037 INFO /usr/local/bin/bench watch
2025-01-28 22:32:56,044 INFO /usr/local/bin/bench schedule
2025-01-28 22:32:56,104 INFO /usr/local/bin/bench serve --port 8000
2025-01-28 22:32:56,109 INFO /usr/local/bin/bench worker
2025-01-28 23:44:20,044 INFO /usr/local/bin/bench start
2025-01-28 23:44:20,251 INFO /usr/local/bin/bench watch
2025-01-28 23:44:20,301 INFO /usr/local/bin/bench schedule
2025-01-28 23:44:20,317 INFO /usr/local/bin/bench serve --port 8000
2025-01-28 23:44:20,328 INFO /usr/local/bin/bench worker
2025-01-28 23:46:43,828 INFO /usr/local/bin/bench start
2025-01-28 23:46:44,020 INFO /usr/local/bin/bench serve --port 8000
2025-01-28 23:46:44,023 INFO /usr/local/bin/bench watch
2025-01-28 23:46:44,076 INFO /usr/local/bin/bench worker
2025-01-28 23:46:44,090 INFO /usr/local/bin/bench schedule
2025-01-28 23:49:10,222 INFO /usr/local/bin/bench start
2025-01-28 23:49:10,463 INFO /usr/local/bin/bench schedule
2025-01-28 23:49:10,466 INFO /usr/local/bin/bench serve --port 8000
2025-01-28 23:49:10,479 INFO /usr/local/bin/bench watch
2025-01-28 23:49:10,493 INFO /usr/local/bin/bench worker
2025-01-28 23:56:14,683 INFO /usr/local/bin/bench start
2025-01-28 23:56:14,887 INFO /usr/local/bin/bench watch
2025-01-28 23:56:14,926 INFO /usr/local/bin/bench worker
2025-01-28 23:56:14,945 INFO /usr/local/bin/bench serve --port 8000
2025-01-28 23:56:14,948 INFO /usr/local/bin/bench schedule
2025-01-28 23:57:22,078 INFO /usr/local/bin/bench start
2025-01-28 23:57:22,305 INFO /usr/local/bin/bench watch
2025-01-28 23:57:22,311 INFO /usr/local/bin/bench serve --port 8000
2025-01-28 23:57:22,312 INFO /usr/local/bin/bench schedule
2025-01-28 23:57:22,387 INFO /usr/local/bin/bench worker
2025-01-29 00:00:01,379 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-29 00:00:05,901 INFO /usr/local/bin/bench start
2025-01-29 00:00:06,105 INFO /usr/local/bin/bench watch
2025-01-29 00:00:06,110 INFO /usr/local/bin/bench serve --port 8000
2025-01-29 00:00:06,162 INFO /usr/local/bin/bench schedule
2025-01-29 00:00:06,179 INFO /usr/local/bin/bench worker
2025-01-29 00:00:52,560 INFO /usr/local/bin/bench start
2025-01-29 00:00:52,786 INFO /usr/local/bin/bench worker
2025-01-29 00:00:52,792 INFO /usr/local/bin/bench serve --port 8000
2025-01-29 00:00:52,818 INFO /usr/local/bin/bench watch
2025-01-29 00:00:52,834 INFO /usr/local/bin/bench schedule
2025-01-29 11:04:39,248 INFO /usr/local/bin/bench start
2025-01-29 11:04:39,482 INFO /usr/local/bin/bench schedule
2025-01-29 11:04:39,549 INFO /usr/local/bin/bench serve --port 8000
2025-01-29 11:04:39,555 INFO /usr/local/bin/bench watch
2025-01-29 11:04:39,555 INFO /usr/local/bin/bench worker
2025-01-29 12:00:01,503 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-29 15:50:37,002 INFO /usr/local/bin/bench start
2025-01-29 15:50:37,222 INFO /usr/local/bin/bench serve --port 8000
2025-01-29 15:50:37,241 INFO /usr/local/bin/bench schedule
2025-01-29 15:50:37,276 INFO /usr/local/bin/bench worker
2025-01-29 15:50:37,283 INFO /usr/local/bin/bench watch
2025-01-29 17:25:37,365 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-29 17:25:42,242 INFO /usr/local/bin/bench start
2025-01-29 17:25:42,445 INFO /usr/local/bin/bench watch
2025-01-29 17:25:42,462 INFO /usr/local/bin/bench serve --port 8000
2025-01-29 17:25:42,507 INFO /usr/local/bin/bench schedule
2025-01-29 17:25:42,524 INFO /usr/local/bin/bench worker
2025-01-29 17:25:51,797 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-29 18:00:02,121 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-30 12:00:01,695 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-30 12:09:18,705 INFO /usr/local/bin/bench start
2025-01-30 12:09:18,937 INFO /usr/local/bin/bench worker
2025-01-30 12:09:18,951 INFO /usr/local/bin/bench schedule
2025-01-30 12:09:18,983 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 12:09:18,996 INFO /usr/local/bin/bench watch
2025-01-30 12:10:54,880 INFO /usr/local/bin/bench start
2025-01-30 12:10:55,102 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 12:10:55,117 INFO /usr/local/bin/bench schedule
2025-01-30 12:10:55,160 INFO /usr/local/bin/bench watch
2025-01-30 12:10:55,163 INFO /usr/local/bin/bench worker
2025-01-30 12:14:46,125 INFO /usr/local/bin/bench --site child_ngo build
2025-01-30 12:15:16,692 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-30 12:32:07,979 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-01-30 12:32:14,473 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-01-30 12:32:20,008 INFO /usr/local/bin/bench start
2025-01-30 12:32:20,217 INFO /usr/local/bin/bench worker
2025-01-30 12:32:20,257 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 12:32:20,262 INFO /usr/local/bin/bench schedule
2025-01-30 12:32:20,276 INFO /usr/local/bin/bench watch
2025-01-30 12:37:45,500 INFO /usr/local/bin/bench start
2025-01-30 12:37:45,701 INFO /usr/local/bin/bench schedule
2025-01-30 12:37:45,707 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 12:37:45,708 INFO /usr/local/bin/bench watch
2025-01-30 12:37:45,777 INFO /usr/local/bin/bench worker
2025-01-30 12:46:25,913 INFO /usr/local/bin/bench build
2025-01-30 12:48:43,263 INFO /usr/local/bin/bench build
2025-01-30 12:49:05,734 INFO /usr/local/bin/bench start
2025-01-30 12:49:05,933 INFO /usr/local/bin/bench schedule
2025-01-30 12:49:05,949 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 12:49:05,997 INFO /usr/local/bin/bench watch
2025-01-30 12:49:06,013 INFO /usr/local/bin/bench worker
2025-01-30 12:49:13,855 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-01-30 12:49:18,310 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-01-30 12:49:22,381 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-30 12:54:02,283 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-01-30 12:54:05,829 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-01-30 12:54:11,507 INFO /usr/local/bin/bench start
2025-01-30 12:54:11,820 INFO /usr/local/bin/bench schedule
2025-01-30 12:54:11,833 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 12:54:11,834 INFO /usr/local/bin/bench watch
2025-01-30 12:54:11,839 INFO /usr/local/bin/bench worker
2025-01-30 12:58:19,427 INFO /usr/local/bin/bench build --force
2025-01-30 12:58:56,219 INFO /usr/local/bin/bench start
2025-01-30 12:58:56,410 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 12:58:56,416 INFO /usr/local/bin/bench worker
2025-01-30 12:58:56,419 INFO /usr/local/bin/bench schedule
2025-01-30 12:58:56,480 INFO /usr/local/bin/bench watch
2025-01-30 12:59:09,052 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-01-30 12:59:11,320 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-01-30 12:59:15,156 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-30 13:01:39,711 INFO /usr/local/bin/bench start
2025-01-30 13:01:39,897 INFO /usr/local/bin/bench schedule
2025-01-30 13:01:39,952 INFO /usr/local/bin/bench worker
2025-01-30 13:01:39,956 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 13:01:39,966 INFO /usr/local/bin/bench watch
2025-01-30 13:04:08,350 INFO /usr/local/bin/bench --site child_ngo mariadb
2025-01-30 13:07:51,792 INFO /usr/local/bin/bench --site child_ngo execute frappe.utils.background_jobs.clear_failed_jobs
2025-01-30 13:09:36,941 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-30 13:18:51,990 INFO /usr/local/bin/bench start
2025-01-30 13:18:52,184 INFO /usr/local/bin/bench watch
2025-01-30 13:18:52,216 INFO /usr/local/bin/bench schedule
2025-01-30 13:18:52,241 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 13:18:52,256 INFO /usr/local/bin/bench worker
2025-01-30 13:19:01,518 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-30 13:23:52,461 INFO /usr/local/bin/bench start
2025-01-30 13:23:52,713 INFO /usr/local/bin/bench worker
2025-01-30 13:23:52,774 INFO /usr/local/bin/bench schedule
2025-01-30 13:23:52,790 INFO /usr/local/bin/bench watch
2025-01-30 13:23:52,790 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 13:26:58,997 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-01-30 13:27:05,809 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-01-30 13:27:17,500 INFO /usr/local/bin/bench start
2025-01-30 13:27:17,779 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 13:27:17,789 INFO /usr/local/bin/bench worker
2025-01-30 13:27:17,793 INFO /usr/local/bin/bench watch
2025-01-30 13:27:17,795 INFO /usr/local/bin/bench schedule
2025-01-30 16:51:57,356 INFO /usr/local/bin/bench start
2025-01-30 16:51:57,545 INFO /usr/local/bin/bench schedule
2025-01-30 16:51:57,546 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 16:51:57,552 INFO /usr/local/bin/bench watch
2025-01-30 16:51:57,621 INFO /usr/local/bin/bench worker
2025-01-30 16:52:02,183 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-30 17:00:00,969 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-01-30 17:00:03,638 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-01-30 17:00:06,444 INFO /usr/local/bin/bench start
2025-01-30 17:00:06,643 INFO /usr/local/bin/bench worker
2025-01-30 17:00:06,648 INFO /usr/local/bin/bench watch
2025-01-30 17:00:06,695 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 17:00:06,708 INFO /usr/local/bin/bench schedule
2025-01-30 17:00:10,918 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-30 17:01:18,840 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-01-30 17:01:24,363 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-01-30 17:01:28,752 INFO /usr/local/bin/bench start
2025-01-30 17:01:29,015 INFO /usr/local/bin/bench watch
2025-01-30 17:01:29,016 INFO /usr/local/bin/bench worker
2025-01-30 17:01:29,057 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 17:01:29,070 INFO /usr/local/bin/bench schedule
2025-01-30 17:01:36,433 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-30 17:04:14,096 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-01-30 17:04:16,668 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-01-30 17:04:18,727 INFO /usr/local/bin/bench start
2025-01-30 17:04:18,927 INFO /usr/local/bin/bench watch
2025-01-30 17:04:18,929 INFO /usr/local/bin/bench schedule
2025-01-30 17:04:18,935 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 17:04:18,994 INFO /usr/local/bin/bench worker
2025-01-30 17:04:25,943 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-30 17:22:41,459 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-01-30 17:22:48,045 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-01-30 17:22:51,579 INFO /usr/local/bin/bench start
2025-01-30 17:22:51,782 INFO /usr/local/bin/bench watch
2025-01-30 17:22:51,782 INFO /usr/local/bin/bench schedule
2025-01-30 17:22:51,841 INFO /usr/local/bin/bench worker
2025-01-30 17:22:51,854 INFO /usr/local/bin/bench serve --port 8000
2025-01-30 17:22:55,786 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-30 18:00:01,976 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-31 00:00:01,528 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-31 06:00:01,401 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-31 08:16:22,525 INFO /usr/local/bin/bench start
2025-01-31 08:16:22,709 INFO /usr/local/bin/bench schedule
2025-01-31 08:16:22,749 INFO /usr/local/bin/bench watch
2025-01-31 08:16:22,754 INFO /usr/local/bin/bench serve --port 8000
2025-01-31 08:16:22,758 INFO /usr/local/bin/bench worker
2025-01-31 12:00:02,059 INFO /usr/local/bin/bench --verbose --site all backup
2025-01-31 13:52:43,810 INFO /usr/local/bin/bench start
2025-01-31 13:52:44,027 INFO /usr/local/bin/bench serve --port 8000
2025-01-31 13:52:44,027 INFO /usr/local/bin/bench worker
2025-01-31 13:52:44,075 INFO /usr/local/bin/bench schedule
2025-01-31 13:52:44,080 INFO /usr/local/bin/bench watch
2025-01-31 13:53:02,283 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-31 16:50:53,623 INFO /usr/local/bin/bench start
2025-01-31 16:50:53,882 INFO /usr/local/bin/bench watch
2025-01-31 16:50:53,894 INFO /usr/local/bin/bench schedule
2025-01-31 16:50:53,936 INFO /usr/local/bin/bench serve --port 8000
2025-01-31 16:50:53,946 INFO /usr/local/bin/bench worker
2025-01-31 17:01:44,444 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-31 17:04:35,035 INFO /usr/local/bin/bench --site child_ngo migrate
2025-01-31 18:00:01,125 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-01 18:00:02,119 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-02 10:53:35,083 INFO /usr/local/bin/bench start
2025-02-02 10:53:35,275 INFO /usr/local/bin/bench schedule
2025-02-02 10:53:35,295 INFO /usr/local/bin/bench serve --port 8000
2025-02-02 10:53:35,299 INFO /usr/local/bin/bench watch
2025-02-02 10:53:35,325 INFO /usr/local/bin/bench worker
2025-02-02 12:00:01,915 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-02 18:00:01,291 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-03 12:00:01,300 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-03 12:10:24,565 INFO /usr/local/bin/bench start
2025-02-03 12:10:24,789 INFO /usr/local/bin/bench watch
2025-02-03 12:10:24,792 INFO /usr/local/bin/bench worker
2025-02-03 12:10:24,840 INFO /usr/local/bin/bench serve --port 8000
2025-02-03 12:10:24,857 INFO /usr/local/bin/bench schedule
2025-02-03 18:00:01,373 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-04 00:00:01,623 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-04 06:00:01,303 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-04 12:00:01,392 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-04 12:34:56,166 INFO /usr/local/bin/bench start
2025-02-04 12:34:56,380 INFO /usr/local/bin/bench schedule
2025-02-04 12:34:56,399 INFO /usr/local/bin/bench worker
2025-02-04 12:34:56,430 INFO /usr/local/bin/bench watch
2025-02-04 12:34:56,438 INFO /usr/local/bin/bench serve --port 8000
2025-02-05 18:00:01,913 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-05 20:35:33,479 INFO /usr/local/bin/bench start
2025-02-05 20:35:33,691 INFO /usr/local/bin/bench watch
2025-02-05 20:35:33,695 INFO /usr/local/bin/bench worker
2025-02-05 20:35:33,702 INFO /usr/local/bin/bench schedule
2025-02-05 20:35:33,724 INFO /usr/local/bin/bench serve --port 8000
2025-02-05 20:44:08,552 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-05 21:03:40,643 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-05 21:09:41,543 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-05 22:02:32,892 INFO /usr/local/bin/bench --site child_ngo clear_cache
2025-02-05 22:02:40,312 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-02-05 22:02:47,052 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-02-05 22:06:21,855 INFO /usr/local/bin/bench --site child_ngo console
2025-02-05 22:20:45,412 INFO /usr/local/bin/bench --site child_ngo console
2025-02-06 00:00:01,174 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-06 06:00:01,564 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-06 12:00:01,269 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-06 12:36:53,893 INFO /usr/local/bin/bench start
2025-02-06 12:36:54,076 INFO /usr/local/bin/bench serve --port 8000
2025-02-06 12:36:54,133 INFO /usr/local/bin/bench schedule
2025-02-06 12:36:54,138 INFO /usr/local/bin/bench worker
2025-02-06 12:36:54,146 INFO /usr/local/bin/bench watch
2025-02-06 18:00:01,124 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-06 23:29:23,658 INFO /usr/local/bin/bench --site child_ngo console
2025-02-07 00:00:01,702 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-07 06:00:01,815 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-07 12:00:02,026 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-07 17:15:21,040 INFO /usr/local/bin/bench start
2025-02-07 17:15:21,238 INFO /usr/local/bin/bench worker
2025-02-07 17:15:21,259 INFO /usr/local/bin/bench schedule
2025-02-07 17:15:21,299 INFO /usr/local/bin/bench serve --port 8000
2025-02-07 17:15:21,303 INFO /usr/local/bin/bench watch
2025-02-07 18:00:01,843 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-07 18:34:23,088 INFO /usr/local/bin/bench start
2025-02-07 18:34:23,339 INFO /usr/local/bin/bench watch
2025-02-07 18:34:23,387 INFO /usr/local/bin/bench serve --port 8000
2025-02-07 18:34:23,398 INFO /usr/local/bin/bench schedule
2025-02-07 18:34:23,402 INFO /usr/local/bin/bench worker
2025-02-07 19:21:53,490 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-07 19:28:39,012 INFO /usr/local/bin/bench start
2025-02-07 19:28:39,254 INFO /usr/local/bin/bench serve --port 8000
2025-02-07 19:28:39,262 INFO /usr/local/bin/bench watch
2025-02-07 19:28:39,330 INFO /usr/local/bin/bench worker
2025-02-07 19:28:39,330 INFO /usr/local/bin/bench schedule
2025-02-08 00:00:01,987 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-08 06:00:01,905 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-08 10:05:36,066 INFO /usr/local/bin/bench start
2025-02-08 10:05:36,252 INFO /usr/local/bin/bench watch
2025-02-08 10:05:36,257 INFO /usr/local/bin/bench schedule
2025-02-08 10:05:36,312 INFO /usr/local/bin/bench serve --port 8000
2025-02-08 10:05:36,316 INFO /usr/local/bin/bench worker
2025-02-08 10:06:11,027 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-08 11:10:05,960 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-08 11:13:24,986 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-08 11:20:18,782 INFO /usr/local/bin/bench start
2025-02-08 11:20:19,127 INFO /usr/local/bin/bench serve --port 8000
2025-02-08 11:20:19,130 INFO /usr/local/bin/bench worker
2025-02-08 11:20:19,132 INFO /usr/local/bin/bench watch
2025-02-08 11:20:19,133 INFO /usr/local/bin/bench schedule
2025-02-08 11:29:01,568 INFO /usr/local/bin/bench --site child_ngo console
2025-02-08 12:00:01,716 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-08 18:00:01,396 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-09 10:34:21,830 INFO /usr/local/bin/bench start
2025-02-09 10:34:22,040 INFO /usr/local/bin/bench watch
2025-02-09 10:34:22,093 INFO /usr/local/bin/bench serve --port 8000
2025-02-09 10:34:22,099 INFO /usr/local/bin/bench schedule
2025-02-09 10:34:22,100 INFO /usr/local/bin/bench worker
2025-02-09 11:22:48,992 INFO /usr/local/bin/bench start
2025-02-09 11:22:49,181 INFO /usr/local/bin/bench watch
2025-02-09 11:22:49,182 INFO /usr/local/bin/bench serve --port 8000
2025-02-09 11:22:49,188 INFO /usr/local/bin/bench schedule
2025-02-09 11:22:49,255 INFO /usr/local/bin/bench worker
2025-02-09 11:23:06,350 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-09 12:00:02,167 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-09 13:29:44,268 INFO /usr/local/bin/bench start
2025-02-09 13:29:44,606 INFO /usr/local/bin/bench watch
2025-02-09 13:29:44,612 INFO /usr/local/bin/bench worker
2025-02-09 13:29:44,625 INFO /usr/local/bin/bench serve --port 8000
2025-02-09 13:29:44,625 INFO /usr/local/bin/bench schedule
2025-02-09 13:29:52,270 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-02-09 13:29:57,657 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-02-09 15:42:07,751 INFO /usr/local/bin/bench start
2025-02-09 15:42:07,959 INFO /usr/local/bin/bench worker
2025-02-09 15:42:07,959 INFO /usr/local/bin/bench serve --port 8000
2025-02-09 15:42:07,983 INFO /usr/local/bin/bench schedule
2025-02-09 15:42:07,994 INFO /usr/local/bin/bench watch
2025-02-10 11:18:00,806 INFO /usr/local/bin/bench start
2025-02-10 11:18:01,079 INFO /usr/local/bin/bench watch
2025-02-10 11:18:01,080 INFO /usr/local/bin/bench serve --port 8000
2025-02-10 11:18:01,099 INFO /usr/local/bin/bench schedule
2025-02-10 11:18:01,106 INFO /usr/local/bin/bench worker
2025-02-10 12:00:02,079 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-11 12:00:01,351 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-11 17:07:37,644 INFO /usr/local/bin/bench start
2025-02-11 17:07:37,918 INFO /usr/local/bin/bench serve --port 8000
2025-02-11 17:07:37,920 INFO /usr/local/bin/bench worker
2025-02-11 17:07:37,933 INFO /usr/local/bin/bench watch
2025-02-11 17:07:37,944 INFO /usr/local/bin/bench schedule
2025-02-11 17:08:21,233 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-11 17:09:17,658 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-11 17:24:05,997 INFO /usr/local/bin/bench --site child_ngo show-pending-jobs
2025-02-11 17:44:59,995 INFO /usr/local/bin/bench start
2025-02-11 17:45:00,290 INFO /usr/local/bin/bench watch
2025-02-11 17:45:00,337 INFO /usr/local/bin/bench serve --port 8000
2025-02-11 17:45:00,349 INFO /usr/local/bin/bench schedule
2025-02-11 17:45:00,356 INFO /usr/local/bin/bench worker
2025-02-11 17:45:06,079 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-11 18:00:01,895 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-13 15:22:07,669 INFO /usr/local/bin/bench --site child_ngo add-to-hosts
2025-02-13 15:22:29,575 INFO /usr/local/bin/bench start
2025-02-13 15:22:29,877 INFO /usr/local/bin/bench worker
2025-02-13 15:22:29,886 INFO /usr/local/bin/bench serve --port 8000
2025-02-13 15:22:29,886 INFO /usr/local/bin/bench schedule
2025-02-13 15:22:29,887 INFO /usr/local/bin/bench watch
2025-02-13 18:00:01,555 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-14 00:00:01,915 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-14 06:00:01,384 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-14 12:00:01,914 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-14 18:00:01,462 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-15 12:00:02,121 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-15 18:00:01,784 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-16 12:00:02,199 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-16 13:30:55,692 INFO /usr/local/bin/bench start
2025-02-16 13:30:55,959 INFO /usr/local/bin/bench schedule
2025-02-16 13:30:55,967 INFO /usr/local/bin/bench watch
2025-02-16 13:30:55,977 INFO /usr/local/bin/bench worker
2025-02-16 13:30:55,993 INFO /usr/local/bin/bench serve --port 8000
2025-02-16 13:31:03,341 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-16 16:23:11,072 INFO /usr/local/bin/bench get-app https://github.com/BrainWise-DEV/DeviceScraper.git
2025-02-16 16:23:11,080 LOG Getting DeviceScraper
2025-02-16 16:23:11,080 DEBUG cd ./apps && git clone https://github.com/BrainWise-DEV/DeviceScraper.git  --depth 1 --origin upstream
2025-02-16 16:23:48,690 WARNING /usr/local/bin/bench get-app https://github.com/BrainWise-DEV/DeviceScraper.git executed with exit code 1
2025-02-16 16:27:16,151 INFO /usr/local/bin/bench get-app https://github.com/BrainWise-DEV/DeviceScraper.git
2025-02-16 16:27:16,159 LOG Getting DeviceScraper
2025-02-16 16:27:16,159 DEBUG cd ./apps && git clone https://github.com/BrainWise-DEV/DeviceScraper.git  --depth 1 --origin upstream
2025-02-16 16:27:32,310 LOG Installing device_scraper
2025-02-16 16:27:32,310 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/device_scraper 
2025-02-16 16:27:45,527 DEBUG cd /home/<USER>/frappe-bench/apps/device_scraper && yarn install --check-files
2025-02-16 16:27:54,156 DEBUG bench build --app device_scraper
2025-02-16 16:27:54,345 INFO /usr/local/bin/bench build --app device_scraper
2025-02-16 16:27:57,076 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-02-16 18:00:02,147 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-17 12:00:01,967 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-17 12:07:49,052 INFO /usr/local/bin/bench start
2025-02-17 12:07:49,275 INFO /usr/local/bin/bench schedule
2025-02-17 12:07:49,279 INFO /usr/local/bin/bench serve --port 8000
2025-02-17 12:07:49,293 INFO /usr/local/bin/bench watch
2025-02-17 12:07:49,316 INFO /usr/local/bin/bench worker
2025-02-17 12:08:15,813 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-17 12:22:04,629 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-17 12:23:04,201 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-17 12:41:02,883 INFO /usr/local/bin/bench --site child_ngo clear_cache
2025-02-17 12:41:18,040 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-02-17 12:41:25,641 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-02-17 15:44:00,070 INFO /usr/local/bin/bench start
2025-02-17 15:44:00,272 INFO /usr/local/bin/bench serve --port 8000
2025-02-17 15:44:00,278 INFO /usr/local/bin/bench worker
2025-02-17 15:44:00,279 INFO /usr/local/bin/bench schedule
2025-02-17 15:44:00,346 INFO /usr/local/bin/bench watch
2025-02-17 17:25:42,110 INFO /usr/local/bin/bench start
2025-02-17 17:25:42,343 INFO /usr/local/bin/bench worker
2025-02-17 17:25:42,352 INFO /usr/local/bin/bench watch
2025-02-17 17:25:42,412 INFO /usr/local/bin/bench serve --port 8000
2025-02-17 17:25:42,417 INFO /usr/local/bin/bench schedule
2025-02-17 17:33:06,750 INFO /usr/local/bin/bench start
2025-02-17 17:33:06,952 INFO /usr/local/bin/bench serve --port 8000
2025-02-17 17:33:06,955 INFO /usr/local/bin/bench schedule
2025-02-17 17:33:07,012 INFO /usr/local/bin/bench watch
2025-02-17 17:33:07,028 INFO /usr/local/bin/bench worker
2025-02-17 17:33:15,390 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-02-17 17:33:17,773 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-02-17 17:36:58,095 INFO /usr/local/bin/bench build
2025-02-17 17:43:07,438 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-17 18:00:02,037 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-18 10:21:21,454 INFO /usr/local/bin/bench start
2025-02-18 10:21:21,842 INFO /usr/local/bin/bench serve --port 8000
2025-02-18 10:21:21,854 INFO /usr/local/bin/bench watch
2025-02-18 10:21:21,854 INFO /usr/local/bin/bench worker
2025-02-18 10:21:21,860 INFO /usr/local/bin/bench schedule
2025-02-18 12:00:01,728 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-18 13:58:44,144 INFO /usr/local/bin/bench start
2025-02-18 13:58:44,340 INFO /usr/local/bin/bench schedule
2025-02-18 13:58:44,346 INFO /usr/local/bin/bench worker
2025-02-18 13:58:44,412 INFO /usr/local/bin/bench serve --port 8000
2025-02-18 13:58:44,416 INFO /usr/local/bin/bench watch
2025-02-18 13:58:54,200 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-18 18:00:01,273 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-19 12:00:01,425 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-19 12:47:55,240 INFO /usr/local/bin/bench start
2025-02-19 12:47:55,450 INFO /usr/local/bin/bench watch
2025-02-19 12:47:55,478 INFO /usr/local/bin/bench worker
2025-02-19 12:47:55,498 INFO /usr/local/bin/bench serve --port 8000
2025-02-19 12:47:55,517 INFO /usr/local/bin/bench schedule
2025-02-19 12:47:59,643 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-19 16:20:05,639 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-19 16:21:08,349 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-20 12:28:34,376 INFO /usr/local/bin/bench start
2025-02-20 12:28:34,578 INFO /usr/local/bin/bench worker
2025-02-20 12:28:34,620 INFO /usr/local/bin/bench schedule
2025-02-20 12:28:34,623 INFO /usr/local/bin/bench serve --port 8000
2025-02-20 12:28:34,626 INFO /usr/local/bin/bench watch
2025-02-20 12:28:52,588 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-20 15:57:55,067 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-20 15:59:46,812 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-20 16:15:00,782 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-20 16:15:48,204 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-20 16:51:05,402 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-02-20 16:51:11,591 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-02-20 17:50:07,485 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-20 18:00:01,447 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-20 20:32:46,550 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-20 20:45:29,671 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-21 00:00:01,967 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-21 06:00:01,899 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-21 18:00:01,853 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-21 19:10:44,580 INFO /usr/local/bin/bench start
2025-02-21 19:10:44,804 INFO /usr/local/bin/bench watch
2025-02-21 19:10:44,824 INFO /usr/local/bin/bench worker
2025-02-21 19:10:44,863 INFO /usr/local/bin/bench serve --port 8000
2025-02-21 19:10:44,864 INFO /usr/local/bin/bench schedule
2025-02-21 19:10:49,266 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-21 22:15:26,703 INFO /usr/local/bin/bench start
2025-02-21 22:15:26,889 INFO /usr/local/bin/bench worker
2025-02-21 22:15:26,900 INFO /usr/local/bin/bench serve --port 8000
2025-02-21 22:15:26,936 INFO /usr/local/bin/bench schedule
2025-02-21 22:15:26,951 INFO /usr/local/bin/bench watch
2025-02-22 12:00:01,847 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-22 18:00:02,203 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-23 12:00:01,964 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-23 13:01:59,024 INFO /usr/local/bin/bench start
2025-02-23 13:01:59,248 INFO /usr/local/bin/bench worker
2025-02-23 13:01:59,256 INFO /usr/local/bin/bench schedule
2025-02-23 13:01:59,316 INFO /usr/local/bin/bench serve --port 8000
2025-02-23 13:01:59,317 INFO /usr/local/bin/bench watch
2025-02-23 17:30:04,797 INFO /usr/local/bin/bench start
2025-02-23 17:30:05,031 INFO /usr/local/bin/bench schedule
2025-02-23 17:30:05,040 INFO /usr/local/bin/bench watch
2025-02-23 17:30:05,102 INFO /usr/local/bin/bench serve --port 8000
2025-02-23 17:30:05,106 INFO /usr/local/bin/bench worker
2025-02-23 17:30:34,513 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-24 11:12:07,512 INFO /usr/local/bin/bench migrate
2025-02-24 11:12:16,096 INFO /usr/local/bin/bench start
2025-02-24 11:12:16,353 INFO /usr/local/bin/bench schedule
2025-02-24 11:12:16,358 INFO /usr/local/bin/bench serve --port 8000
2025-02-24 11:12:16,371 INFO /usr/local/bin/bench watch
2025-02-24 11:12:16,383 INFO /usr/local/bin/bench worker
2025-02-24 11:12:34,261 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-24 16:06:52,339 INFO /usr/local/bin/bench start
2025-02-24 16:06:52,832 INFO /usr/local/bin/bench serve --port 8000
2025-02-24 16:06:52,861 INFO /usr/local/bin/bench schedule
2025-02-24 16:06:52,863 INFO /usr/local/bin/bench watch
2025-02-24 16:06:52,971 INFO /usr/local/bin/bench worker
2025-02-24 16:31:10,197 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-24 16:35:22,153 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-24 16:57:33,888 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-24 16:59:17,966 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-24 17:19:15,615 INFO /usr/local/bin/bench start
2025-02-24 17:19:15,894 INFO /usr/local/bin/bench schedule
2025-02-24 17:19:15,911 INFO /usr/local/bin/bench watch
2025-02-24 17:19:15,914 INFO /usr/local/bin/bench worker
2025-02-24 17:19:15,918 INFO /usr/local/bin/bench serve --port 8000
2025-02-24 18:00:02,122 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-25 11:06:00,698 INFO /usr/local/bin/bench start
2025-02-25 11:06:01,098 INFO /usr/local/bin/bench serve --port 8000
2025-02-25 11:06:01,124 INFO /usr/local/bin/bench worker
2025-02-25 11:06:01,131 INFO /usr/local/bin/bench watch
2025-02-25 11:06:01,136 INFO /usr/local/bin/bench schedule
2025-02-25 11:06:20,975 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-25 11:08:15,286 INFO /usr/local/bin/bench --site child_ngo console
2025-02-25 11:10:27,522 INFO /usr/local/bin/bench --site child_ngo console
2025-02-25 11:12:16,785 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-25 11:12:41,328 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-25 12:00:02,463 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-25 15:07:08,889 INFO /usr/local/bin/bench start
2025-02-25 15:07:09,106 INFO /usr/local/bin/bench schedule
2025-02-25 15:07:09,107 INFO /usr/local/bin/bench watch
2025-02-25 15:07:09,111 INFO /usr/local/bin/bench worker
2025-02-25 15:07:09,149 INFO /usr/local/bin/bench serve --port 8000
2025-02-25 15:21:12,174 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-02-25 15:21:18,836 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-02-25 16:24:20,352 INFO /usr/local/bin/bench start
2025-02-25 16:24:20,551 INFO /usr/local/bin/bench schedule
2025-02-25 16:24:20,596 INFO /usr/local/bin/bench serve --port 8000
2025-02-25 16:24:20,597 INFO /usr/local/bin/bench watch
2025-02-25 16:24:20,605 INFO /usr/local/bin/bench worker
2025-02-25 16:25:18,115 INFO /usr/local/bin/bench npm install --save-dev @types/cypress
2025-02-25 16:36:08,864 INFO /usr/local/bin/bench start
2025-02-25 16:36:09,105 INFO /usr/local/bin/bench schedule
2025-02-25 16:36:09,107 INFO /usr/local/bin/bench serve --port 8000
2025-02-25 16:36:09,115 INFO /usr/local/bin/bench worker
2025-02-25 16:36:09,121 INFO /usr/local/bin/bench watch
2025-02-25 16:37:16,845 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-25 16:37:56,587 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-02-25 16:38:00,623 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-02-25 16:52:14,218 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-25 16:52:54,664 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-02-25 16:52:57,720 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-02-25 18:00:02,068 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-26 12:00:01,163 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-26 15:54:32,931 INFO /usr/local/bin/bench start
2025-02-26 15:54:33,122 INFO /usr/local/bin/bench serve --port 8000
2025-02-26 15:54:33,124 INFO /usr/local/bin/bench schedule
2025-02-26 15:54:33,171 INFO /usr/local/bin/bench watch
2025-02-26 15:54:33,181 INFO /usr/local/bin/bench worker
2025-02-26 16:11:10,478 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-02-26 16:11:15,056 INFO /usr/local/bin/bench --site child_ngo migrate
2025-02-27 12:00:01,450 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-27 16:50:58,287 INFO /usr/local/bin/bench start
2025-02-27 16:50:58,496 INFO /usr/local/bin/bench worker
2025-02-27 16:50:58,505 INFO /usr/local/bin/bench watch
2025-02-27 16:50:58,512 INFO /usr/local/bin/bench schedule
2025-02-27 16:50:58,523 INFO /usr/local/bin/bench serve --port 8000
2025-02-27 18:00:01,374 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-28 00:00:02,099 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-28 06:00:01,177 INFO /usr/local/bin/bench --verbose --site all backup
2025-02-28 18:00:01,406 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-01 18:00:02,223 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-02 10:37:26,432 INFO /usr/local/bin/bench start
2025-03-02 10:37:26,760 INFO /usr/local/bin/bench watch
2025-03-02 10:37:26,761 INFO /usr/local/bin/bench worker
2025-03-02 10:37:26,775 INFO /usr/local/bin/bench schedule
2025-03-02 10:37:26,783 INFO /usr/local/bin/bench serve --port 8000
2025-03-02 10:37:55,375 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-02 12:00:01,991 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-02 14:24:19,532 INFO /usr/local/bin/bench start
2025-03-02 14:24:19,754 INFO /usr/local/bin/bench worker
2025-03-02 14:24:19,771 INFO /usr/local/bin/bench schedule
2025-03-02 14:24:19,783 INFO /usr/local/bin/bench serve --port 8000
2025-03-02 14:24:19,798 INFO /usr/local/bin/bench watch
2025-03-02 14:24:25,419 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-03 10:44:32,016 INFO /usr/local/bin/bench start
2025-03-03 10:44:32,204 INFO /usr/local/bin/bench schedule
2025-03-03 10:44:32,207 INFO /usr/local/bin/bench worker
2025-03-03 10:44:32,214 INFO /usr/local/bin/bench serve --port 8000
2025-03-03 10:44:32,251 INFO /usr/local/bin/bench watch
2025-03-03 12:00:01,135 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-04 12:00:01,987 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-04 13:15:06,721 INFO /usr/local/bin/bench start
2025-03-04 13:15:06,933 INFO /usr/local/bin/bench worker
2025-03-04 13:15:06,937 INFO /usr/local/bin/bench watch
2025-03-04 13:15:06,973 INFO /usr/local/bin/bench schedule
2025-03-04 13:15:06,982 INFO /usr/local/bin/bench serve --port 8000
2025-03-04 18:00:02,061 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-05 00:00:01,332 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-05 12:00:01,630 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-05 18:00:02,013 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-05 19:23:44,152 INFO /usr/local/bin/bench start
2025-03-05 19:23:44,391 INFO /usr/local/bin/bench watch
2025-03-05 19:23:44,396 INFO /usr/local/bin/bench schedule
2025-03-05 19:23:44,438 INFO /usr/local/bin/bench serve --port 8000
2025-03-05 19:23:44,446 INFO /usr/local/bin/bench worker
2025-03-05 19:24:02,127 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-06 12:43:06,827 INFO /usr/local/bin/bench start
2025-03-06 12:43:07,011 INFO /usr/local/bin/bench worker
2025-03-06 12:43:07,014 INFO /usr/local/bin/bench watch
2025-03-06 12:43:07,016 INFO /usr/local/bin/bench schedule
2025-03-06 12:43:07,071 INFO /usr/local/bin/bench serve --port 8000
2025-03-06 17:12:24,062 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-06 17:13:15,220 INFO /usr/local/bin/bench start
2025-03-06 17:13:15,487 INFO /usr/local/bin/bench worker
2025-03-06 17:13:15,503 INFO /usr/local/bin/bench serve --port 8000
2025-03-06 17:13:15,537 INFO /usr/local/bin/bench watch
2025-03-06 17:13:15,537 INFO /usr/local/bin/bench schedule
2025-03-06 18:00:01,438 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-06 18:03:23,747 INFO /usr/local/bin/bench start
2025-03-06 18:03:24,020 INFO /usr/local/bin/bench watch
2025-03-06 18:03:24,053 INFO /usr/local/bin/bench serve --port 8000
2025-03-06 18:03:24,077 INFO /usr/local/bin/bench schedule
2025-03-06 18:03:24,086 INFO /usr/local/bin/bench worker
2025-03-06 18:03:26,652 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-07 12:00:01,534 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-07 18:00:01,324 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-08 00:00:01,797 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-08 12:00:01,185 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-09 05:13:41,442 INFO /usr/local/bin/bench start
2025-03-09 05:13:41,680 INFO /usr/local/bin/bench worker
2025-03-09 05:13:41,690 INFO /usr/local/bin/bench schedule
2025-03-09 05:13:41,693 INFO /usr/local/bin/bench serve --port 8000
2025-03-09 05:13:41,695 INFO /usr/local/bin/bench watch
2025-03-09 06:00:02,192 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-09 09:57:43,623 INFO /usr/local/bin/bench start
2025-03-09 09:57:43,903 INFO /usr/local/bin/bench serve --port 8000
2025-03-09 09:57:43,907 INFO /usr/local/bin/bench schedule
2025-03-09 09:57:43,911 INFO /usr/local/bin/bench worker
2025-03-09 09:57:43,982 INFO /usr/local/bin/bench watch
2025-03-09 12:00:01,579 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-09 12:46:56,704 INFO /usr/local/bin/bench --site child_ngo set-admin-password root
2025-03-09 12:47:28,246 INFO /usr/local/bin/bench start
2025-03-09 12:47:28,480 INFO /usr/local/bin/bench serve --port 8000
2025-03-09 12:47:28,483 INFO /usr/local/bin/bench watch
2025-03-09 12:47:28,483 INFO /usr/local/bin/bench worker
2025-03-09 12:47:28,539 INFO /usr/local/bin/bench schedule
2025-03-09 12:48:59,975 INFO /usr/local/bin/bench --site child_ngo set-admin-password root
2025-03-09 12:49:16,756 INFO /usr/local/bin/bench --site child_ngo clear-cashe
2025-03-09 12:49:23,338 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-03-09 12:49:30,681 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-03-09 12:50:34,935 INFO /usr/local/bin/bench browse child_ngo --user Administrator
2025-03-09 12:55:12,060 INFO /usr/local/bin/bench --site child_ngo set-admin-password root
2025-03-09 12:56:54,413 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-09 12:57:52,188 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-03-09 12:57:56,412 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-03-09 12:58:03,926 INFO /usr/local/bin/bench --site child_ngo set-admin-password root
2025-03-09 13:03:16,105 INFO /usr/local/bin/bench browse child_ngo --user Administrator
2025-03-09 13:14:40,100 INFO /usr/local/bin/bench --site child_ngo set-admin-password admin
2025-03-09 13:25:15,399 INFO /usr/local/bin/bench start
2025-03-09 13:25:15,800 INFO /usr/local/bin/bench watch
2025-03-09 13:25:15,815 INFO /usr/local/bin/bench schedule
2025-03-09 13:25:15,823 INFO /usr/local/bin/bench worker
2025-03-09 13:25:15,831 INFO /usr/local/bin/bench serve --port 8000
2025-03-09 14:09:29,045 INFO /usr/local/bin/bench start
2025-03-09 14:09:29,225 INFO /usr/local/bin/bench serve --port 8000
2025-03-09 14:09:29,237 INFO /usr/local/bin/bench worker
2025-03-09 14:09:29,251 INFO /usr/local/bin/bench watch
2025-03-09 14:09:29,256 INFO /usr/local/bin/bench schedule
2025-03-09 14:10:08,185 INFO /usr/local/bin/bench --site childngo migrate
2025-03-09 14:10:19,044 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-09 14:10:54,752 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-03-09 14:11:01,435 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-03-09 14:11:14,491 INFO /usr/local/bin/bench start
2025-03-09 14:11:14,741 INFO /usr/local/bin/bench serve --port 8000
2025-03-09 14:11:14,741 INFO /usr/local/bin/bench schedule
2025-03-09 14:11:14,743 INFO /usr/local/bin/bench watch
2025-03-09 14:11:14,746 INFO /usr/local/bin/bench worker
2025-03-10 06:00:01,236 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-10 12:00:02,169 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-11 06:00:01,370 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-11 12:00:01,891 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-12 00:00:01,567 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-12 11:24:00,320 INFO /usr/local/bin/bench start
2025-03-12 11:24:00,524 INFO /usr/local/bin/bench serve --port 8000
2025-03-12 11:24:00,557 INFO /usr/local/bin/bench worker
2025-03-12 11:24:00,570 INFO /usr/local/bin/bench watch
2025-03-12 11:24:00,570 INFO /usr/local/bin/bench schedule
2025-03-12 12:00:01,645 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-12 18:00:01,212 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-13 00:00:01,381 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-13 13:12:05,846 INFO /usr/local/bin/bench start
2025-03-13 13:12:06,064 INFO /usr/local/bin/bench worker
2025-03-13 13:12:06,081 INFO /usr/local/bin/bench serve --port 8000
2025-03-13 13:12:06,101 INFO /usr/local/bin/bench schedule
2025-03-13 13:12:06,106 INFO /usr/local/bin/bench watch
2025-03-13 13:16:48,345 INFO /usr/local/bin/bench start
2025-03-13 13:16:48,557 INFO /usr/local/bin/bench watch
2025-03-13 13:16:48,561 INFO /usr/local/bin/bench serve --port 8000
2025-03-13 13:16:48,606 INFO /usr/local/bin/bench worker
2025-03-13 13:16:48,616 INFO /usr/local/bin/bench schedule
2025-03-13 13:16:58,837 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-03-13 13:17:02,031 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-03-13 18:00:01,301 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-14 00:00:01,577 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-14 06:00:01,741 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-14 12:00:01,691 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-16 11:12:43,514 INFO /usr/local/bin/bench start
2025-03-16 11:12:43,743 INFO /usr/local/bin/bench worker
2025-03-16 11:12:43,778 INFO /usr/local/bin/bench schedule
2025-03-16 11:12:43,778 INFO /usr/local/bin/bench serve --port 8000
2025-03-16 11:12:43,784 INFO /usr/local/bin/bench watch
2025-03-16 11:32:23,568 INFO /usr/local/bin/bench start
2025-03-16 11:32:24,019 INFO /usr/local/bin/bench serve --port 8000
2025-03-16 11:32:24,073 INFO /usr/local/bin/bench schedule
2025-03-16 11:32:24,097 INFO /usr/local/bin/bench worker
2025-03-16 11:32:24,122 INFO /usr/local/bin/bench watch
2025-03-16 12:00:01,779 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-16 18:00:01,923 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-17 10:46:14,421 INFO /usr/local/bin/bench start
2025-03-17 10:46:14,615 INFO /usr/local/bin/bench serve --port 8000
2025-03-17 10:46:14,659 INFO /usr/local/bin/bench schedule
2025-03-17 10:46:14,667 INFO /usr/local/bin/bench worker
2025-03-17 10:46:14,677 INFO /usr/local/bin/bench watch
2025-03-17 10:46:45,336 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-17 12:00:01,214 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-17 18:00:01,568 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-18 00:00:01,733 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-18 00:01:50,638 INFO /usr/local/bin/bench start
2025-03-18 00:01:51,012 INFO /usr/local/bin/bench watch
2025-03-18 00:01:51,012 INFO /usr/local/bin/bench serve --port 8000
2025-03-18 00:01:51,017 INFO /usr/local/bin/bench worker
2025-03-18 00:01:51,042 INFO /usr/local/bin/bench schedule
2025-03-18 00:40:41,697 INFO /usr/local/bin/bench start
2025-03-18 00:40:41,892 INFO /usr/local/bin/bench watch
2025-03-18 00:40:41,916 INFO /usr/local/bin/bench schedule
2025-03-18 00:40:41,930 INFO /usr/local/bin/bench serve --port 8000
2025-03-18 00:40:41,954 INFO /usr/local/bin/bench worker
2025-03-18 11:57:25,299 INFO /usr/local/bin/bench start
2025-03-18 11:57:25,504 INFO /usr/local/bin/bench serve --port 8000
2025-03-18 11:57:25,544 INFO /usr/local/bin/bench worker
2025-03-18 11:57:25,554 INFO /usr/local/bin/bench watch
2025-03-18 11:57:25,559 INFO /usr/local/bin/bench schedule
2025-03-18 12:00:01,558 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-18 12:19:53,001 INFO /usr/local/bin/bench start
2025-03-18 12:19:53,209 INFO /usr/local/bin/bench schedule
2025-03-18 12:19:53,244 INFO /usr/local/bin/bench watch
2025-03-18 12:19:53,257 INFO /usr/local/bin/bench serve --port 8000
2025-03-18 12:19:53,261 INFO /usr/local/bin/bench worker
2025-03-18 12:24:15,579 INFO /usr/local/bin/bench start
2025-03-18 12:24:15,782 INFO /usr/local/bin/bench schedule
2025-03-18 12:24:15,784 INFO /usr/local/bin/bench watch
2025-03-18 12:24:15,833 INFO /usr/local/bin/bench serve --port 8000
2025-03-18 12:24:15,842 INFO /usr/local/bin/bench worker
2025-03-18 12:24:30,012 INFO /usr/local/bin/bench build
2025-03-18 12:33:12,088 INFO /usr/local/bin/bench start
2025-03-18 12:33:12,300 INFO /usr/local/bin/bench worker
2025-03-18 12:33:12,302 INFO /usr/local/bin/bench serve --port 8000
2025-03-18 12:33:12,324 INFO /usr/local/bin/bench schedule
2025-03-18 12:33:12,336 INFO /usr/local/bin/bench watch
2025-03-18 12:43:36,871 INFO /usr/local/bin/bench start
2025-03-18 12:43:37,108 INFO /usr/local/bin/bench schedule
2025-03-18 12:43:37,126 INFO /usr/local/bin/bench serve --port 8000
2025-03-18 12:43:37,139 INFO /usr/local/bin/bench watch
2025-03-18 12:43:37,172 INFO /usr/local/bin/bench worker
2025-03-18 12:43:50,467 INFO /usr/local/bin/bench --site child_ngo build
2025-03-18 13:04:30,515 INFO /usr/local/bin/bench --site child_ngo build
2025-03-18 13:10:55,236 INFO /usr/local/bin/bench --site child_ngo build
2025-03-18 13:14:23,338 INFO /usr/local/bin/bench start
2025-03-18 13:14:23,566 INFO /usr/local/bin/bench worker
2025-03-18 13:14:23,588 INFO /usr/local/bin/bench watch
2025-03-18 13:14:23,633 INFO /usr/local/bin/bench serve --port 8000
2025-03-18 13:14:23,636 INFO /usr/local/bin/bench schedule
2025-03-18 13:14:30,282 INFO /usr/local/bin/bench --site child_ngo build
2025-03-18 15:02:58,022 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-18 16:19:37,838 INFO /usr/local/bin/bench start
2025-03-18 16:19:38,058 INFO /usr/local/bin/bench worker
2025-03-18 16:19:38,084 INFO /usr/local/bin/bench serve --port 8000
2025-03-18 16:19:38,105 INFO /usr/local/bin/bench watch
2025-03-18 16:19:38,118 INFO /usr/local/bin/bench schedule
2025-03-18 16:19:46,703 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-18 18:00:01,533 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-19 06:00:01,274 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-19 11:54:35,292 INFO /usr/local/bin/bench start
2025-03-19 11:54:35,513 INFO /usr/local/bin/bench schedule
2025-03-19 11:54:35,516 INFO /usr/local/bin/bench worker
2025-03-19 11:54:35,554 INFO /usr/local/bin/bench serve --port 8000
2025-03-19 11:54:35,557 INFO /usr/local/bin/bench watch
2025-03-19 12:00:01,549 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-20 00:00:01,321 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-20 06:00:01,319 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-20 10:52:06,359 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-20 10:52:40,689 INFO /usr/local/bin/bench start
2025-03-20 10:52:40,911 INFO /usr/local/bin/bench schedule
2025-03-20 10:52:40,917 INFO /usr/local/bin/bench worker
2025-03-20 10:52:40,923 INFO /usr/local/bin/bench watch
2025-03-20 10:52:40,954 INFO /usr/local/bin/bench serve --port 8000
2025-03-20 10:52:44,336 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-20 10:53:12,009 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-20 12:00:01,484 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-20 18:00:02,118 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-21 06:00:01,986 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-21 18:00:01,578 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-22 00:00:01,332 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-22 18:00:01,561 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-23 10:38:54,131 INFO /usr/local/bin/bench start
2025-03-23 10:38:54,461 INFO /usr/local/bin/bench worker
2025-03-23 10:38:54,466 INFO /usr/local/bin/bench serve --port 8000
2025-03-23 10:38:54,470 INFO /usr/local/bin/bench watch
2025-03-23 10:38:54,501 INFO /usr/local/bin/bench schedule
2025-03-23 12:00:01,388 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-23 14:17:10,108 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-24 18:00:01,252 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-25 00:00:01,499 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-25 06:00:01,708 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-25 12:00:01,793 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-25 15:31:24,476 INFO /usr/local/bin/bench start
2025-03-25 15:31:24,687 INFO /usr/local/bin/bench worker
2025-03-25 15:31:24,697 INFO /usr/local/bin/bench serve --port 8000
2025-03-25 15:31:24,733 INFO /usr/local/bin/bench watch
2025-03-25 15:31:24,743 INFO /usr/local/bin/bench schedule
2025-03-25 15:31:28,920 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-25 18:00:01,437 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-26 00:00:01,267 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-26 12:00:02,087 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-26 12:23:12,508 INFO /usr/local/bin/bench start
2025-03-26 12:23:12,708 INFO /usr/local/bin/bench serve --port 8000
2025-03-26 12:23:12,709 INFO /usr/local/bin/bench schedule
2025-03-26 12:23:12,763 INFO /usr/local/bin/bench worker
2025-03-26 12:23:12,769 INFO /usr/local/bin/bench watch
2025-03-26 15:20:22,620 INFO /usr/local/bin/bench --site child_ngo mariadb
2025-03-26 18:00:01,952 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-27 10:39:36,859 INFO /usr/local/bin/bench start
2025-03-27 10:39:37,067 INFO /usr/local/bin/bench watch
2025-03-27 10:39:37,077 INFO /usr/local/bin/bench serve --port 8000
2025-03-27 10:39:37,129 INFO /usr/local/bin/bench schedule
2025-03-27 10:39:37,131 INFO /usr/local/bin/bench worker
2025-03-27 12:00:01,365 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-27 12:07:46,213 INFO /usr/local/bin/bench --site child_ngo migrate
2025-03-27 18:00:01,594 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-28 18:00:02,029 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-29 00:00:01,461 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-29 06:00:01,458 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-29 12:00:01,614 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-29 18:00:01,706 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-30 00:00:02,008 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-30 12:00:02,023 INFO /usr/local/bin/bench --verbose --site all backup
2025-03-30 18:00:01,587 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-01 00:00:01,761 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-01 06:00:01,802 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-01 16:40:30,719 INFO /usr/local/bin/bench start
2025-04-01 16:40:31,035 INFO /usr/local/bin/bench schedule
2025-04-01 16:40:31,046 INFO /usr/local/bin/bench watch
2025-04-01 16:40:31,050 INFO /usr/local/bin/bench worker
2025-04-01 16:40:31,062 INFO /usr/local/bin/bench serve --port 8000
2025-04-01 18:00:01,833 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-02 00:00:01,888 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-02 06:00:01,255 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-02 12:00:01,693 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-02 18:00:01,324 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-03 00:00:01,481 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-03 06:00:01,238 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-03 18:00:01,316 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-04 00:00:01,980 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-04 18:00:01,799 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-05 00:00:01,937 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-05 06:00:01,325 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-05 12:00:02,119 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-06 00:00:40,266 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-06 12:00:01,512 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-06 12:24:03,490 INFO /usr/local/bin/bench start
2025-04-06 12:24:03,791 INFO /usr/local/bin/bench schedule
2025-04-06 12:24:03,802 INFO /usr/local/bin/bench worker
2025-04-06 12:24:03,807 INFO /usr/local/bin/bench watch
2025-04-06 12:24:03,844 INFO /usr/local/bin/bench serve --port 8000
2025-04-07 06:00:01,506 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-07 12:00:02,148 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-07 12:39:59,476 INFO /usr/local/bin/bench get-app --branch dev parent_ngo **************:BrainWise-DEV/ParentNGO.git
2025-04-07 12:39:59,487 LOG Getting ParentNGO
2025-04-07 12:39:59,487 DEBUG cd ./apps && <NAME_EMAIL>:BrainWise-DEV/ParentNGO.git --branch dev --depth 1 --origin upstream
2025-04-07 12:40:02,165 LOG Installing parent_ngo
2025-04-07 12:40:02,165 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/parent_ngo 
2025-04-07 12:40:05,253 DEBUG bench build --app parent_ngo
2025-04-07 12:40:05,385 INFO /usr/local/bin/bench build --app parent_ngo
2025-04-07 12:40:07,895 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-04-07 12:47:49,594 INFO /usr/local/bin/bench new-site parent_ngo
2025-04-07 12:48:38,369 INFO /usr/local/bin/bench --site parent_ngo list-apps
2025-04-07 12:49:36,439 INFO /usr/local/bin/bench new-site parent_NGO
2025-04-07 12:57:20,923 INFO /usr/local/bin/bench --site parent_NGO list-apps
2025-04-07 12:57:31,497 INFO /usr/local/bin/bench --site parent_NGO add-to-hosts
2025-04-07 12:57:39,855 INFO /usr/local/bin/bench --site parent_NGO enable-scheduler
2025-04-07 12:57:54,240 INFO /usr/local/bin/bench --site parent_NGO set-maintenance-mode off
2025-04-07 12:58:06,800 INFO /usr/local/bin/bench --site parent_NGO set-config server_script_enabled true
2025-04-07 13:01:53,222 INFO /usr/local/bin/bench start
2025-04-07 13:01:53,492 INFO /usr/local/bin/bench serve --port 8000
2025-04-07 13:01:53,509 INFO /usr/local/bin/bench watch
2025-04-07 13:01:53,513 INFO /usr/local/bin/bench schedule
2025-04-07 13:01:53,541 INFO /usr/local/bin/bench worker
2025-04-07 13:18:22,829 INFO /usr/local/bin/bench start
2025-04-07 13:18:23,046 INFO /usr/local/bin/bench worker
2025-04-07 13:18:23,046 INFO /usr/local/bin/bench schedule
2025-04-07 13:18:23,070 INFO /usr/local/bin/bench watch
2025-04-07 13:18:23,113 INFO /usr/local/bin/bench serve --port 8000
2025-04-07 13:19:03,798 INFO /usr/local/bin/bench --site parent_NGO install-app parent_ngo
2025-04-07 13:21:03,556 INFO /usr/local/bin/bench --site parent_NGO list-apps
2025-04-07 13:25:46,026 INFO /usr/local/bin/bench drop-site parent_NGO
2025-04-07 13:27:27,789 INFO /usr/local/bin/bench list-sites
2025-04-07 14:01:37,998 INFO /usr/local/bin/bench start
2025-04-07 14:01:38,205 INFO /usr/local/bin/bench schedule
2025-04-07 14:01:38,255 INFO /usr/local/bin/bench serve --port 8000
2025-04-07 14:01:38,259 INFO /usr/local/bin/bench watch
2025-04-07 14:01:38,261 INFO /usr/local/bin/bench worker
2025-04-07 14:02:01,667 INFO /usr/local/bin/bench --site child_ngo migrate
2025-04-07 15:38:13,372 INFO /usr/local/bin/bench start
2025-04-07 15:38:13,606 INFO /usr/local/bin/bench worker
2025-04-07 15:38:13,608 INFO /usr/local/bin/bench serve --port 8000
2025-04-07 15:38:13,616 INFO /usr/local/bin/bench watch
2025-04-07 15:38:13,675 INFO /usr/local/bin/bench schedule
2025-04-07 15:38:23,713 INFO /usr/local/bin/bench --site child_ngo clear-cache
2025-04-07 15:38:30,448 INFO /usr/local/bin/bench --site child_ngo clear-website-cache
2025-04-07 15:47:01,208 INFO /usr/local/bin/bench --site child_ngo console
2025-04-07 16:17:22,919 INFO /usr/local/bin/bench --site child_ngo console
2025-04-08 13:30:18,931 INFO /usr/local/bin/bench start
2025-04-08 13:30:19,230 INFO /usr/local/bin/bench watch
2025-04-08 13:30:19,242 INFO /usr/local/bin/bench worker
2025-04-08 13:30:19,259 INFO /usr/local/bin/bench serve --port 8000
2025-04-08 13:30:19,263 INFO /usr/local/bin/bench schedule
2025-04-09 00:00:01,869 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-09 12:00:01,623 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-09 12:04:46,124 INFO /usr/local/bin/bench start
2025-04-09 12:04:46,323 INFO /usr/local/bin/bench worker
2025-04-09 12:04:46,365 INFO /usr/local/bin/bench serve --port 8000
2025-04-09 12:04:46,374 INFO /usr/local/bin/bench watch
2025-04-09 12:04:46,381 INFO /usr/local/bin/bench schedule
2025-04-09 18:00:01,405 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-10 12:00:01,517 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-10 18:00:02,010 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-11 18:00:01,586 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-12 00:00:01,267 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-12 18:00:02,213 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-13 00:00:01,759 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-13 11:09:09,623 INFO /usr/local/bin/bench list-apps
2025-04-13 11:10:12,065 INFO /usr/local/bin/bench new-site parentngo
2025-04-13 11:11:17,420 INFO /usr/local/bin/bench --site parentngo add-to-hosts
2025-04-13 11:11:34,404 INFO /usr/local/bin/bench --site parentngo enable-scheduler
2025-04-13 11:11:46,041 INFO /usr/local/bin/bench --site parentngo set-maintenance-mode off
2025-04-13 11:11:58,682 INFO /usr/local/bin/bench --site parentngo set-config server_script_enabled true
2025-04-13 11:12:35,735 INFO /usr/local/bin/bench start
2025-04-13 11:12:35,967 INFO /usr/local/bin/bench schedule
2025-04-13 11:12:35,972 INFO /usr/local/bin/bench watch
2025-04-13 11:12:35,981 INFO /usr/local/bin/bench serve --port 8000
2025-04-13 11:12:35,987 INFO /usr/local/bin/bench worker
2025-04-13 11:13:21,471 INFO /usr/local/bin/bench --site parentngo install-app parent_ngo
2025-04-13 11:40:28,018 INFO /usr/local/bin/bench --site parentngo restore /home/<USER>/Documents/Parent_NGO/20250113_161850-parent_ngo-database.sql.gz --with-public-files /home/<USER>/Documents/Parent_NGO/20250113_161850-parent_ngo-files.tar --with-private-files /home/<USER>/Documents/Parent_NGO/20250113_161850-parent_ngo-private-files.tar
2025-04-13 11:43:04,694 INFO /usr/local/bin/bench --site parentngo set-admin-password root
2025-04-13 12:00:01,564 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-14 12:00:01,449 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-15 12:00:01,467 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-15 18:00:02,016 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-16 12:00:02,167 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-18 00:00:02,010 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-18 06:00:01,339 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-18 12:00:02,006 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-20 12:00:01,798 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-21 12:00:01,360 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-21 18:00:01,497 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-22 00:00:01,730 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-22 06:00:01,840 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-22 12:00:02,025 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-22 18:00:01,769 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-23 00:00:01,621 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-23 12:00:01,879 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-23 18:00:01,497 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-25 20:23:15,295 INFO /usr/local/bin/bench start
2025-04-25 20:23:15,500 INFO /usr/local/bin/bench watch
2025-04-25 20:23:15,531 INFO /usr/local/bin/bench serve --port 8000
2025-04-25 20:23:15,539 INFO /usr/local/bin/bench worker
2025-04-25 20:23:15,555 INFO /usr/local/bin/bench schedule
2025-04-26 00:00:01,916 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-26 06:00:02,101 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-26 12:00:01,958 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-26 18:00:01,669 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-27 00:00:01,365 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-27 06:00:01,726 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-27 12:00:02,109 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-28 00:00:01,708 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-28 06:00:01,773 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-28 10:53:02,400 INFO /usr/local/bin/bench start
2025-04-28 10:53:02,658 INFO /usr/local/bin/bench worker
2025-04-28 10:53:02,661 INFO /usr/local/bin/bench serve --port 8000
2025-04-28 10:53:02,663 INFO /usr/local/bin/bench watch
2025-04-28 10:53:02,692 INFO /usr/local/bin/bench schedule
2025-04-28 11:54:06,485 INFO /usr/local/bin/bench start
2025-04-28 11:54:06,728 INFO /usr/local/bin/bench serve --port 8000
2025-04-28 11:54:06,734 INFO /usr/local/bin/bench watch
2025-04-28 11:54:06,782 INFO /usr/local/bin/bench schedule
2025-04-28 11:54:06,787 INFO /usr/local/bin/bench worker
2025-04-28 11:59:28,593 INFO /usr/local/bin/bench --site parentngo clear-cache
2025-04-28 11:59:34,823 INFO /usr/local/bin/bench --site parentngo clear-website-cache
2025-04-28 11:59:44,299 INFO /usr/local/bin/bench --site parentngo migrate
2025-04-28 12:00:01,884 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-28 13:02:47,914 INFO /usr/local/bin/bench --site parentngo migrate
2025-04-28 13:03:13,792 INFO /usr/local/bin/bench start
2025-04-28 13:03:14,053 INFO /usr/local/bin/bench schedule
2025-04-28 13:03:14,089 INFO /usr/local/bin/bench serve --port 8000
2025-04-28 13:03:14,090 INFO /usr/local/bin/bench watch
2025-04-28 13:03:14,131 INFO /usr/local/bin/bench worker
2025-04-29 12:00:01,654 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-29 18:00:01,938 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-30 00:00:02,042 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-30 06:00:01,937 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-30 12:00:02,049 INFO /usr/local/bin/bench --verbose --site all backup
2025-04-30 18:00:01,289 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-01 00:00:01,653 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-01 11:50:11,443 INFO /usr/local/bin/bench start
2025-05-01 11:50:11,670 INFO /usr/local/bin/bench worker
2025-05-01 11:50:11,712 INFO /usr/local/bin/bench serve --port 8000
2025-05-01 11:50:11,714 INFO /usr/local/bin/bench watch
2025-05-01 11:50:11,720 INFO /usr/local/bin/bench schedule
2025-05-01 12:00:01,354 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-01 12:22:01,828 INFO /usr/local/bin/bench start
2025-05-01 12:22:02,052 INFO /usr/local/bin/bench serve --port 8000
2025-05-01 12:22:02,052 INFO /usr/local/bin/bench schedule
2025-05-01 12:22:02,110 INFO /usr/local/bin/bench watch
2025-05-01 12:22:02,112 INFO /usr/local/bin/bench worker
2025-05-01 18:00:01,206 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-02 18:00:01,876 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-03 00:00:01,243 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-04 10:42:58,378 INFO /usr/local/bin/bench start
2025-05-04 10:42:58,659 INFO /usr/local/bin/bench watch
2025-05-04 10:42:58,704 INFO /usr/local/bin/bench serve --port 8000
2025-05-04 10:42:58,707 INFO /usr/local/bin/bench schedule
2025-05-04 10:42:58,719 INFO /usr/local/bin/bench worker
2025-05-04 12:00:01,837 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-04 15:21:05,879 INFO /usr/local/bin/bench --site parentngo mariadb
2025-05-04 15:54:15,096 INFO /usr/local/bin/bench --site parentngo console
2025-05-04 18:00:01,848 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-05 12:00:01,344 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-05 13:28:48,750 INFO /usr/local/bin/bench start
2025-05-05 13:28:49,123 INFO /usr/local/bin/bench serve --port 8000
2025-05-05 13:28:49,124 INFO /usr/local/bin/bench worker
2025-05-05 13:28:49,126 INFO /usr/local/bin/bench schedule
2025-05-05 13:28:49,142 INFO /usr/local/bin/bench watch
2025-05-05 14:27:43,922 INFO /usr/local/bin/bench start
2025-05-05 14:27:44,286 INFO /usr/local/bin/bench watch
2025-05-05 14:27:44,287 INFO /usr/local/bin/bench worker
2025-05-05 14:27:44,290 INFO /usr/local/bin/bench schedule
2025-05-05 14:27:44,314 INFO /usr/local/bin/bench serve --port 8000
2025-05-05 14:57:46,516 INFO /usr/local/bin/bench --site parent-ngo console
2025-05-05 14:58:01,708 INFO /usr/local/bin/bench --site parentngo console
2025-05-05 15:22:30,412 INFO /usr/local/bin/bench start
2025-05-05 15:22:30,745 INFO /usr/local/bin/bench watch
2025-05-05 15:22:30,755 INFO /usr/local/bin/bench worker
2025-05-05 15:22:30,755 INFO /usr/local/bin/bench schedule
2025-05-05 15:22:30,759 INFO /usr/local/bin/bench serve --port 8000
2025-05-05 15:23:52,145 INFO /usr/local/bin/bench start
2025-05-05 15:23:56,876 INFO /usr/local/bin/bench start
2025-05-05 15:24:05,093 INFO /usr/local/bin/bench start
2025-05-05 15:24:31,030 INFO /usr/local/bin/bench restart
2025-05-05 15:24:31,042 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-05-05 15:24:31,486 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-05-05 15:24:34,947 INFO /usr/local/bin/bench start
2025-05-05 15:24:51,252 INFO /usr/local/bin/bench start
2025-05-05 15:25:24,518 INFO /usr/local/bin/bench start
2025-05-05 15:25:26,027 INFO /usr/local/bin/bench start
2025-05-05 15:25:27,320 INFO /usr/local/bin/bench start
2025-05-05 15:25:29,107 INFO /usr/local/bin/bench start
2025-05-05 15:25:30,312 INFO /usr/local/bin/bench start
2025-05-05 15:25:31,640 INFO /usr/local/bin/bench start
2025-05-05 15:25:32,661 INFO /usr/local/bin/bench start
2025-05-05 15:25:33,504 INFO /usr/local/bin/bench start
2025-05-05 15:27:26,061 INFO /usr/local/bin/bench start
2025-05-05 15:33:50,373 INFO /usr/local/bin/bench start
2025-05-05 15:35:05,752 INFO /usr/local/bin/bench start
2025-05-05 15:35:05,955 INFO /usr/local/bin/bench watch
2025-05-05 15:35:05,962 INFO /usr/local/bin/bench schedule
2025-05-05 15:35:06,003 INFO /usr/local/bin/bench serve --port 8000
2025-05-05 15:35:06,009 INFO /usr/local/bin/bench worker
2025-05-05 15:36:26,723 INFO /usr/local/bin/bench start
2025-05-05 15:36:26,924 INFO /usr/local/bin/bench schedule
2025-05-05 15:36:26,966 INFO /usr/local/bin/bench watch
2025-05-05 15:36:26,984 INFO /usr/local/bin/bench worker
2025-05-05 15:36:26,988 INFO /usr/local/bin/bench serve --port 8000
2025-05-05 15:40:40,798 INFO /usr/local/bin/bench --site parentngo clear_cashe
2025-05-05 15:40:43,746 INFO /usr/local/bin/bench --site parentngo clear_cache
2025-05-05 15:40:50,254 INFO /usr/local/bin/bench --site parentngo clear-cache
2025-05-05 15:40:56,169 INFO /usr/local/bin/bench --site parentngo clear-website-cache
2025-05-05 15:41:01,620 INFO /usr/local/bin/bench --site parentngo build
2025-05-05 15:41:31,028 INFO /usr/local/bin/bench start
2025-05-05 15:41:31,307 INFO /usr/local/bin/bench watch
2025-05-05 15:41:31,318 INFO /usr/local/bin/bench serve --port 8000
2025-05-05 15:41:31,320 INFO /usr/local/bin/bench schedule
2025-05-05 15:41:31,323 INFO /usr/local/bin/bench worker
2025-05-05 18:00:02,102 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-06 06:00:01,249 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-06 12:00:01,359 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-06 18:00:01,744 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-07 00:00:01,531 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-07 12:00:01,307 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-07 15:37:09,518 INFO /usr/local/bin/bench start
2025-05-07 15:37:09,758 INFO /usr/local/bin/bench worker
2025-05-07 15:37:09,802 INFO /usr/local/bin/bench serve --port 8000
2025-05-07 15:37:09,828 INFO /usr/local/bin/bench watch
2025-05-07 15:37:09,835 INFO /usr/local/bin/bench schedule
2025-05-07 15:37:55,981 INFO /usr/local/bin/bench start
2025-05-07 15:37:56,181 INFO /usr/local/bin/bench serve --port 8000
2025-05-07 15:37:56,188 INFO /usr/local/bin/bench schedule
2025-05-07 15:37:56,203 INFO /usr/local/bin/bench worker
2025-05-07 15:37:56,217 INFO /usr/local/bin/bench watch
2025-05-07 15:41:54,236 INFO /usr/local/bin/bench get-app https://github.com/NagariaHussain/doppio
2025-05-07 15:41:54,243 LOG Getting doppio
2025-05-07 15:41:54,243 DEBUG cd ./apps && git clone https://github.com/NagariaHussain/doppio  --depth 1 --origin upstream
2025-05-07 15:41:55,184 LOG Installing doppio
2025-05-07 15:41:55,184 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/doppio 
2025-05-07 15:41:57,023 DEBUG cd /home/<USER>/frappe-bench/apps/doppio && yarn install --check-files
2025-05-07 15:41:58,340 DEBUG bench build --app doppio
2025-05-07 15:41:58,437 INFO /usr/local/bin/bench build --app doppio
2025-05-07 15:42:00,158 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-05-07 15:42:00,423 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-05-07 15:42:12,224 INFO /usr/local/bin/bench get-app --branch main https://github.com/BrainWise-DEV/Nexus
2025-05-07 15:42:12,232 LOG Getting Nexus
2025-05-07 15:42:12,232 DEBUG cd ./apps && git clone https://github.com/BrainWise-DEV/Nexus --branch main --depth 1 --origin upstream
2025-05-07 15:42:57,526 LOG Installing nexus
2025-05-07 15:42:57,527 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/nexus 
2025-05-07 15:42:59,483 DEBUG cd /home/<USER>/frappe-bench/apps/nexus && yarn install --check-files
2025-05-07 15:43:04,356 DEBUG bench build --app nexus
2025-05-07 15:43:04,475 INFO /usr/local/bin/bench build --app nexus
2025-05-07 15:43:10,957 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-05-07 15:43:11,307 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-05-07 15:46:00,965 INFO /usr/local/bin/bench new-site nexus.com
2025-05-07 15:47:15,242 INFO /usr/local/bin/bench --site nexus.com enable-scheduler
2025-05-07 15:47:28,500 INFO /usr/local/bin/bench --site nexus.com set-maintenance-mode off
2025-05-07 15:47:42,557 INFO /usr/local/bin/bench --site nexus.com set-config server_script_enabled true
2025-05-07 15:47:54,715 INFO /usr/local/bin/bench --site nexus.com add-to-hosts
2025-05-07 15:48:19,818 INFO /usr/local/bin/bench --site nexus.com install-app nexus
2025-05-07 15:49:54,352 INFO /usr/local/bin/bench start
2025-05-07 15:49:54,620 INFO /usr/local/bin/bench serve --port 8000
2025-05-07 15:49:54,628 INFO /usr/local/bin/bench worker
2025-05-07 15:49:54,644 INFO /usr/local/bin/bench schedule
2025-05-07 15:49:54,659 INFO /usr/local/bin/bench watch
2025-05-07 15:50:06,159 INFO /usr/local/bin/bench --site nexus.com migrate
2025-05-07 15:54:15,546 INFO /usr/local/bin/bench pip install pre-commit
2025-05-07 15:56:18,894 INFO /usr/local/bin/bench pip install pre-commit
2025-05-07 15:57:19,169 INFO /usr/local/bin/bench pip install pre-commit
2025-05-07 16:02:38,974 INFO /usr/local/bin/bench pip install pre-commit
2025-05-07 16:31:32,593 INFO /usr/local/bin/bench start
2025-05-07 16:31:32,819 INFO /usr/local/bin/bench serve --port 8000
2025-05-07 16:31:32,847 INFO /usr/local/bin/bench worker
2025-05-07 16:31:32,873 INFO /usr/local/bin/bench watch
2025-05-07 16:31:32,876 INFO /usr/local/bin/bench schedule
2025-05-07 16:32:00,833 INFO /usr/local/bin/bench --site nexus.com migrate
2025-05-07 17:04:38,742 INFO /usr/local/bin/bench start
2025-05-07 17:04:39,000 INFO /usr/local/bin/bench worker
2025-05-07 17:04:39,017 INFO /usr/local/bin/bench serve --port 8000
2025-05-07 17:04:39,058 INFO /usr/local/bin/bench watch
2025-05-07 17:04:39,059 INFO /usr/local/bin/bench schedule
2025-05-07 17:21:34,562 INFO /usr/local/bin/bench --site nexus.com build
2025-05-08 10:59:50,592 INFO /usr/local/bin/bench get-app https://github.com/frappe/lms
2025-05-08 10:59:50,602 LOG Getting lms
2025-05-08 10:59:50,602 DEBUG cd ./apps && git clone https://github.com/frappe/lms  --depth 1 --origin upstream
2025-05-08 11:00:03,816 LOG Installing lms
2025-05-08 11:00:03,817 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/lms 
2025-05-08 11:00:11,922 DEBUG cd /home/<USER>/frappe-bench/apps/lms && yarn install --check-files
2025-05-08 11:01:40,760 DEBUG bench build --app lms
2025-05-08 11:01:40,922 INFO /usr/local/bin/bench build --app lms
2025-05-08 11:02:22,422 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-05-08 11:02:22,761 INFO A newer version of bench is available: 5.23.0 → 5.24.1
2025-05-08 12:00:01,989 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-08 18:00:01,391 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-09 18:00:01,534 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-10 00:00:01,689 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-10 00:10:37,137 INFO /usr/local/bin/bench start
2025-05-10 00:10:37,483 INFO /usr/local/bin/bench watch
2025-05-10 00:10:37,485 INFO /usr/local/bin/bench serve --port 8000
2025-05-10 00:10:37,491 INFO /usr/local/bin/bench schedule
2025-05-10 00:10:37,515 INFO /usr/local/bin/bench worker
2025-05-10 00:11:52,278 INFO /usr/local/bin/bench --site nexus.com build
2025-05-10 00:18:42,145 INFO /usr/local/bin/bench --site nexus.com build
2025-05-10 12:00:01,254 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-10 18:00:01,230 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-11 00:00:01,198 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-11 06:00:01,436 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-11 11:19:07,744 INFO /usr/local/bin/bench start
2025-05-11 11:19:08,064 INFO /usr/local/bin/bench worker
2025-05-11 11:19:08,073 INFO /usr/local/bin/bench schedule
2025-05-11 11:19:08,089 INFO /usr/local/bin/bench watch
2025-05-11 11:19:08,091 INFO /usr/local/bin/bench serve --port 8000
2025-05-11 11:25:51,453 INFO /usr/local/bin/bench start
2025-05-11 11:25:51,746 INFO /usr/local/bin/bench serve --port 8000
2025-05-11 11:25:51,747 INFO /usr/local/bin/bench worker
2025-05-11 11:25:51,762 INFO /usr/local/bin/bench schedule
2025-05-11 11:25:51,781 INFO /usr/local/bin/bench watch
2025-05-11 11:26:58,730 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-11 11:39:43,263 INFO /usr/local/bin/bench --site parentngo mariadb
2025-05-11 11:47:29,068 INFO /usr/local/bin/bench --site parentngo clear-cache
2025-05-11 11:47:31,389 INFO /usr/local/bin/bench --site parentngo mariadb
2025-05-11 11:47:38,197 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-11 12:00:01,191 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-11 13:04:17,661 INFO /usr/local/bin/bench --site parentngo install-app iscore
2025-05-11 13:04:27,818 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-11 14:13:47,631 INFO /usr/local/bin/bench --site parentngo console
2025-05-11 16:25:21,708 INFO /usr/local/bin/bench start
2025-05-11 16:25:22,042 INFO /usr/local/bin/bench worker
2025-05-11 16:25:22,062 INFO /usr/local/bin/bench watch
2025-05-11 16:25:22,073 INFO /usr/local/bin/bench serve --port 8000
2025-05-11 16:25:22,080 INFO /usr/local/bin/bench schedule
2025-05-11 16:25:47,271 INFO /usr/local/bin/bench --site nexus.com list-apps
2025-05-11 16:25:53,921 INFO /usr/local/bin/bench --site nexus.com migrate
2025-05-11 16:27:46,372 INFO /usr/local/bin/bench --site nexus.com build
2025-05-11 16:28:40,009 INFO /usr/local/bin/bench --site nexus.com build
2025-05-11 16:29:52,592 INFO /usr/local/bin/bench --site nexus.com build
2025-05-12 00:00:01,815 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-12 06:00:01,361 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-12 10:52:37,091 INFO /usr/local/bin/bench start
2025-05-12 10:52:37,401 INFO /usr/local/bin/bench watch
2025-05-12 10:52:37,411 INFO /usr/local/bin/bench serve --port 8000
2025-05-12 10:52:37,415 INFO /usr/local/bin/bench worker
2025-05-12 10:52:37,416 INFO /usr/local/bin/bench schedule
2025-05-12 10:52:50,758 INFO /usr/local/bin/bench --site nexus.com migrate
2025-05-12 10:53:42,476 INFO /usr/local/bin/bench --site nexus.com build
2025-05-12 10:54:26,039 INFO /usr/local/bin/bench --site nexus.com build
2025-05-12 10:57:31,234 INFO /usr/local/bin/bench --site nexus.com build
2025-05-12 11:07:18,083 INFO /usr/local/bin/bench --site nexus.com build
2025-05-12 11:46:28,449 INFO /usr/local/bin/bench start
2025-05-12 11:46:28,770 INFO /usr/local/bin/bench schedule
2025-05-12 11:46:28,773 INFO /usr/local/bin/bench serve --port 8000
2025-05-12 11:46:28,774 INFO /usr/local/bin/bench worker
2025-05-12 11:46:28,786 INFO /usr/local/bin/bench watch
2025-05-12 11:46:50,599 INFO /usr/local/bin/bench --site nexus.com build
2025-05-12 12:00:02,097 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-12 16:54:31,201 INFO /usr/local/bin/bench --site nexus.com build
2025-05-12 17:20:36,613 INFO /usr/local/bin/bench --site nexus.com build
2025-05-13 12:00:01,774 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-13 15:31:25,241 INFO /usr/local/bin/bench start
2025-05-13 15:31:25,656 INFO /usr/local/bin/bench watch
2025-05-13 15:31:25,677 INFO /usr/local/bin/bench worker
2025-05-13 15:31:25,680 INFO /usr/local/bin/bench schedule
2025-05-13 15:31:25,719 INFO /usr/local/bin/bench serve --port 8000
2025-05-13 15:31:48,796 INFO /usr/local/bin/bench --site ls-sites
2025-05-13 15:31:55,572 INFO /usr/local/bin/bench ls-sites
2025-05-13 15:32:00,924 INFO /usr/local/bin/bench lssites
2025-05-13 15:33:45,182 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-13 16:16:39,824 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-13 16:16:46,945 INFO /usr/local/bin/bench start
2025-05-13 16:16:47,321 INFO /usr/local/bin/bench serve --port 8000
2025-05-13 16:16:47,321 INFO /usr/local/bin/bench worker
2025-05-13 16:16:47,328 INFO /usr/local/bin/bench schedule
2025-05-13 16:16:47,348 INFO /usr/local/bin/bench watch
2025-05-13 16:17:07,562 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-13 16:47:20,502 INFO /usr/local/bin/bench start
2025-05-13 16:47:20,905 INFO /usr/local/bin/bench serve --port 8000
2025-05-13 16:47:20,930 INFO /usr/local/bin/bench watch
2025-05-13 16:47:20,941 INFO /usr/local/bin/bench schedule
2025-05-13 16:47:20,976 INFO /usr/local/bin/bench worker
2025-05-13 16:47:30,514 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-13 16:54:46,923 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-13 18:00:01,641 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-13 18:47:37,346 INFO /usr/local/bin/bench start
2025-05-13 18:47:37,689 INFO /usr/local/bin/bench schedule
2025-05-13 18:47:37,735 INFO /usr/local/bin/bench serve --port 8000
2025-05-13 18:47:37,737 INFO /usr/local/bin/bench worker
2025-05-13 18:47:37,756 INFO /usr/local/bin/bench watch
2025-05-13 18:47:44,883 INFO /usr/local/bin/bench --site nexus.com build
2025-05-13 19:03:43,783 INFO /usr/local/bin/bench --site nexus.com build
2025-05-13 19:09:26,181 INFO /usr/local/bin/bench --site nexus.com build
2025-05-14 00:00:02,058 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-14 06:00:01,846 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-14 12:00:01,594 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-14 16:31:16,561 INFO /usr/local/bin/bench start
2025-05-14 16:31:16,944 INFO /usr/local/bin/bench schedule
2025-05-14 16:31:16,949 INFO /usr/local/bin/bench worker
2025-05-14 16:31:16,963 INFO /usr/local/bin/bench watch
2025-05-14 16:31:16,969 INFO /usr/local/bin/bench serve --port 8000
2025-05-14 16:31:25,193 INFO /usr/local/bin/bench --site nexus.com build
2025-05-14 16:34:27,950 INFO /usr/local/bin/bench start
2025-05-14 16:34:59,248 INFO /usr/local/bin/bench --help
2025-05-14 16:36:07,881 INFO /usr/local/bin/bench list-apps
2025-05-14 16:36:45,273 INFO /usr/local/bin/bench --site nexus.com build
2025-05-14 16:38:22,191 INFO /usr/local/bin/bench start
2025-05-14 16:38:22,389 INFO /usr/local/bin/bench serve --port 8000
2025-05-14 16:38:22,390 INFO /usr/local/bin/bench schedule
2025-05-14 16:38:22,393 INFO /usr/local/bin/bench worker
2025-05-14 16:38:22,438 INFO /usr/local/bin/bench watch
2025-05-14 16:45:01,469 INFO /usr/local/bin/bench --site nexus.com build
2025-05-14 17:05:40,828 INFO /usr/local/bin/bench --site nexus.com build
2025-05-14 17:24:34,300 INFO /usr/local/bin/bench --site nexus.com build
2025-05-14 17:32:13,359 INFO /usr/local/bin/bench --site nexus.com build
2025-05-14 17:58:36,822 INFO /usr/local/bin/bench --site nexus.com build
2025-05-14 18:02:46,940 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-14 21:54:41,197 INFO /usr/local/bin/bench start
2025-05-14 21:54:41,415 INFO /usr/local/bin/bench schedule
2025-05-14 21:54:41,417 INFO /usr/local/bin/bench worker
2025-05-14 21:54:41,424 INFO /usr/local/bin/bench serve --port 8000
2025-05-14 21:54:41,465 INFO /usr/local/bin/bench watch
2025-05-14 22:17:04,809 INFO /usr/local/bin/bench start
2025-05-14 22:17:05,103 INFO /usr/local/bin/bench worker
2025-05-14 22:17:05,109 INFO /usr/local/bin/bench serve --port 8000
2025-05-14 22:17:05,128 INFO /usr/local/bin/bench watch
2025-05-14 22:17:05,129 INFO /usr/local/bin/bench schedule
2025-05-14 22:19:39,355 INFO /usr/local/bin/bench --site nexus.com migrate
2025-05-14 22:20:02,151 INFO /usr/local/bin/bench --site nexus.com build
2025-05-14 22:36:21,877 INFO /usr/local/bin/bench start
2025-05-14 22:36:22,220 INFO /usr/local/bin/bench schedule
2025-05-14 22:36:22,224 INFO /usr/local/bin/bench serve --port 8000
2025-05-14 22:36:22,226 INFO /usr/local/bin/bench worker
2025-05-14 22:36:22,246 INFO /usr/local/bin/bench watch
2025-05-14 22:38:58,128 INFO /usr/local/bin/bench --site {site.name} set-config -p allow_cors *
2025-05-14 22:39:10,923 INFO /usr/local/bin/bench --site nexus.com set-config -p allow_cors *
2025-05-14 22:39:21,295 INFO /usr/local/bin/bench --site nexus.com set-config -p allow-cors *
2025-05-14 22:39:34,553 INFO /usr/local/bin/bench --site nexus.com set-config -p allow_cors *
2025-05-14 22:40:12,421 INFO /usr/local/bin/bench --site nexus.com set-config -p allow_cors *
2025-05-14 22:40:22,277 INFO /usr/local/bin/bench --site nexus.com set-config allow_cors *
2025-05-14 22:42:49,470 INFO /usr/local/bin/bench --site nexus.com build
2025-05-14 22:53:20,058 INFO /usr/local/bin/bench --site nexus.com build
2025-05-15 15:35:57,069 INFO /usr/local/bin/bench start
2025-05-15 15:35:57,373 INFO /usr/local/bin/bench serve --port 8000
2025-05-15 15:35:57,382 INFO /usr/local/bin/bench watch
2025-05-15 15:35:57,386 INFO /usr/local/bin/bench schedule
2025-05-15 15:35:57,389 INFO /usr/local/bin/bench worker
2025-05-15 15:36:32,833 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-15 15:37:36,087 INFO /usr/local/bin/bench --site parentngo mariadb
2025-05-15 15:38:28,115 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-15 15:40:38,400 INFO /usr/local/bin/bench --site parentngo build
2025-05-15 18:00:01,531 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-15 19:51:09,703 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-16 00:00:02,094 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-16 06:00:01,577 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-16 12:00:02,081 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-16 18:00:01,398 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-16 19:03:29,484 INFO /usr/local/bin/bench start
2025-05-16 19:03:29,817 INFO /usr/local/bin/bench worker
2025-05-16 19:03:29,826 INFO /usr/local/bin/bench serve --port 8000
2025-05-16 19:03:29,837 INFO /usr/local/bin/bench watch
2025-05-16 19:03:29,873 INFO /usr/local/bin/bench schedule
2025-05-16 20:20:50,988 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-16 22:32:58,404 INFO /usr/local/bin/bench start
2025-05-16 22:32:58,667 INFO /usr/local/bin/bench watch
2025-05-16 22:32:58,671 INFO /usr/local/bin/bench worker
2025-05-16 22:32:58,678 INFO /usr/local/bin/bench schedule
2025-05-16 22:32:58,708 INFO /usr/local/bin/bench serve --port 8006
2025-05-16 22:41:41,414 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-16 23:05:01,220 INFO /usr/local/bin/bench start
2025-05-16 23:05:01,561 INFO /usr/local/bin/bench serve --port 8006
2025-05-16 23:05:01,564 INFO /usr/local/bin/bench worker
2025-05-16 23:05:01,573 INFO /usr/local/bin/bench watch
2025-05-16 23:05:01,614 INFO /usr/local/bin/bench schedule
2025-05-16 23:21:49,658 INFO /usr/local/bin/bench --site nexus.com migrate
2025-05-16 23:22:22,191 INFO /usr/local/bin/bench --site nexus.com build
2025-05-16 23:23:08,198 INFO /usr/local/bin/bench --site nexus.com build
2025-05-16 23:23:45,048 INFO /usr/local/bin/bench --site nexus.com build
2025-05-16 23:25:44,783 INFO /usr/local/bin/bench --site nexus.com build
2025-05-16 23:28:48,659 INFO /usr/local/bin/bench --site nexus.com build
2025-05-16 23:30:59,607 INFO /usr/local/bin/bench --site nexus.com build
2025-05-16 23:39:53,759 INFO /usr/local/bin/bench --site nexus.com build
2025-05-16 23:49:28,639 INFO /usr/local/bin/bench start
2025-05-16 23:49:28,948 INFO /usr/local/bin/bench watch
2025-05-16 23:49:28,949 INFO /usr/local/bin/bench worker
2025-05-16 23:49:28,955 INFO /usr/local/bin/bench schedule
2025-05-16 23:49:28,968 INFO /usr/local/bin/bench serve --port 8006
2025-05-16 23:49:38,967 INFO /usr/local/bin/bench start
2025-05-16 23:49:39,324 INFO /usr/local/bin/bench serve --port 8000
2025-05-16 23:49:39,336 INFO /usr/local/bin/bench schedule
2025-05-16 23:49:39,341 INFO /usr/local/bin/bench watch
2025-05-16 23:49:39,372 INFO /usr/local/bin/bench worker
2025-05-17 00:00:01,950 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-17 12:00:01,320 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-17 18:00:01,925 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-18 12:00:01,186 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-18 17:27:29,916 INFO /usr/local/bin/bench start
2025-05-18 17:27:30,308 INFO /usr/local/bin/bench schedule
2025-05-18 17:27:30,315 INFO /usr/local/bin/bench worker
2025-05-18 17:27:30,333 INFO /usr/local/bin/bench serve --port 8000
2025-05-18 17:27:30,341 INFO /usr/local/bin/bench watch
2025-05-18 17:27:56,260 INFO /usr/local/bin/bench --site nexus.com build
2025-05-18 17:36:04,133 INFO /usr/local/bin/bench start
2025-05-18 17:38:08,271 INFO /usr/local/bin/bench start
2025-05-18 17:38:08,585 INFO /usr/local/bin/bench watch
2025-05-18 17:38:08,592 INFO /usr/local/bin/bench serve --port 8000
2025-05-18 17:38:08,596 INFO /usr/local/bin/bench schedule
2025-05-18 17:38:08,601 INFO /usr/local/bin/bench worker
2025-05-18 17:38:27,155 INFO /usr/local/bin/bench --site nexus.com build
2025-05-18 17:46:00,886 INFO /usr/local/bin/bench start
2025-05-18 17:46:01,402 INFO /usr/local/bin/bench watch
2025-05-18 17:46:01,450 INFO /usr/local/bin/bench worker
2025-05-18 17:46:01,452 INFO /usr/local/bin/bench serve --port 8000
2025-05-18 17:46:01,559 INFO /usr/local/bin/bench schedule
2025-05-19 11:48:15,832 INFO /usr/local/bin/bench start
2025-05-19 11:48:16,119 INFO /usr/local/bin/bench serve --port 8000
2025-05-19 11:48:16,123 INFO /usr/local/bin/bench watch
2025-05-19 11:48:16,131 INFO /usr/local/bin/bench schedule
2025-05-19 11:48:16,134 INFO /usr/local/bin/bench worker
2025-05-19 12:00:01,953 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-19 14:55:54,898 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-19 15:11:34,575 INFO /usr/local/bin/bench --site child_ngo mariadb
2025-05-19 16:08:57,015 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-19 17:11:20,978 INFO /usr/local/bin/bench start
2025-05-19 17:11:21,291 INFO /usr/local/bin/bench schedule
2025-05-19 17:11:21,299 INFO /usr/local/bin/bench worker
2025-05-19 17:11:21,299 INFO /usr/local/bin/bench watch
2025-05-19 17:11:21,310 INFO /usr/local/bin/bench serve --port 8000
2025-05-19 17:11:32,517 INFO /usr/local/bin/bench --site parentngo clear-cahe
2025-05-19 17:11:35,475 INFO /usr/local/bin/bench --site parentngo clear-cache
2025-05-19 17:11:42,842 INFO /usr/local/bin/bench --site parentngo clear-website-cache
2025-05-20 00:00:01,727 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-20 06:00:01,212 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-20 12:00:01,296 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-20 12:48:50,007 INFO /usr/local/bin/bench start
2025-05-20 12:48:50,516 INFO /usr/local/bin/bench schedule
2025-05-20 12:48:50,554 INFO /usr/local/bin/bench worker
2025-05-20 12:48:50,616 INFO /usr/local/bin/bench serve --port 8000
2025-05-20 12:48:50,641 INFO /usr/local/bin/bench watch
2025-05-20 16:12:45,675 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-20 16:13:35,588 INFO /usr/local/bin/bench --site parentngo clear-cahe
2025-05-20 16:13:45,090 INFO /usr/local/bin/bench --site parentngo clear-cache
2025-05-20 16:13:52,445 INFO /usr/local/bin/bench --site parentngo clear-website-cache
2025-05-20 16:24:04,780 INFO /usr/local/bin/bench start
2025-05-20 16:24:05,169 INFO /usr/local/bin/bench worker
2025-05-20 16:24:05,178 INFO /usr/local/bin/bench serve --port 8000
2025-05-20 16:24:05,181 INFO /usr/local/bin/bench watch
2025-05-20 16:24:05,187 INFO /usr/local/bin/bench schedule
2025-05-20 16:25:31,771 INFO /usr/local/bin/bench --site parentngo build
2025-05-20 16:27:06,077 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-20 16:32:29,277 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-20 18:00:01,858 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-20 18:37:00,936 INFO /usr/local/bin/bench start
2025-05-20 18:37:01,262 INFO /usr/local/bin/bench watch
2025-05-20 18:37:01,266 INFO /usr/local/bin/bench schedule
2025-05-20 18:37:01,270 INFO /usr/local/bin/bench worker
2025-05-20 18:37:01,270 INFO /usr/local/bin/bench serve --port 8000
2025-05-20 21:09:42,398 INFO /usr/local/bin/bench get-app https://github.com/mohamed-ameer/datavalue_theme.git
2025-05-20 21:09:42,408 LOG Getting datavalue_theme
2025-05-20 21:09:42,408 DEBUG cd ./apps && git clone https://github.com/mohamed-ameer/datavalue_theme.git  --depth 1 --origin upstream
2025-05-20 21:09:45,099 LOG Installing datavalue_theme_15
2025-05-20 21:09:45,099 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/datavalue_theme_15 
2025-05-20 21:09:48,050 DEBUG bench build --app datavalue_theme_15
2025-05-20 21:09:48,180 INFO /usr/local/bin/bench build --app datavalue_theme_15
2025-05-20 21:09:52,168 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-05-20 21:09:52,492 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-05-20 21:10:47,388 INFO /usr/local/bin/bench new-site theme.com
2025-05-20 21:14:30,275 INFO /usr/local/bin/bench --site theme.com add-to-hosts
2025-05-20 21:14:54,672 INFO /usr/local/bin/bench --site theme.com enable-scheduler
2025-05-20 21:15:07,533 INFO /usr/local/bin/bench --site theme.com set-maintenance-mode off
2025-05-20 21:15:23,375 INFO /usr/local/bin/bench --site theme.com set-config server_script_enabled true
2025-05-20 21:15:56,717 INFO /usr/local/bin/bench --site theme.com install-app erpnext
2025-05-20 21:17:51,733 INFO /usr/local/bin/bench --site theme.com install-app datavalue_theme_15
2025-05-20 21:18:03,593 INFO /usr/local/bin/bench --site theme.com list-apps
2025-05-20 21:18:13,159 INFO /usr/local/bin/bench start
2025-05-20 21:18:13,509 INFO /usr/local/bin/bench schedule
2025-05-20 21:18:13,538 INFO /usr/local/bin/bench watch
2025-05-20 21:18:13,539 INFO /usr/local/bin/bench serve --port 8000
2025-05-20 21:18:13,550 INFO /usr/local/bin/bench worker
2025-05-20 21:18:35,518 INFO /usr/local/bin/bench --site theme.com migrate
2025-05-20 21:18:59,498 INFO /usr/local/bin/bench --site theme.com build
2025-05-20 21:21:03,841 INFO /usr/local/bin/bench --site theme.com install-app iam
2025-05-20 21:21:15,411 INFO /usr/local/bin/bench start
2025-05-20 21:21:15,751 INFO /usr/local/bin/bench watch
2025-05-20 21:21:15,762 INFO /usr/local/bin/bench serve --port 8000
2025-05-20 21:21:15,791 INFO /usr/local/bin/bench schedule
2025-05-20 21:21:15,856 INFO /usr/local/bin/bench worker
2025-05-20 21:21:20,795 INFO /usr/local/bin/bench --site theme.com migrate
2025-05-21 10:49:41,085 INFO /usr/local/bin/bench start
2025-05-21 10:49:41,426 INFO /usr/local/bin/bench worker
2025-05-21 10:49:41,448 INFO /usr/local/bin/bench serve --port 8000
2025-05-21 10:49:41,450 INFO /usr/local/bin/bench watch
2025-05-21 10:49:41,461 INFO /usr/local/bin/bench schedule
2025-05-21 12:00:01,463 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-21 16:21:21,927 INFO /usr/local/bin/bench start
2025-05-21 16:21:22,283 INFO /usr/local/bin/bench schedule
2025-05-21 16:21:22,297 INFO /usr/local/bin/bench worker
2025-05-21 16:21:22,303 INFO /usr/local/bin/bench watch
2025-05-21 16:21:22,307 INFO /usr/local/bin/bench serve --port 8000
2025-05-21 16:21:39,046 INFO /usr/local/bin/bench --site nexus.com build
2025-05-21 16:37:50,610 INFO /usr/local/bin/bench --site nexus.com migrate
2025-05-21 16:40:23,717 INFO /usr/local/bin/bench --site nexus.com build
2025-05-21 16:46:03,687 INFO /usr/local/bin/bench start
2025-05-21 16:47:06,249 INFO /usr/local/bin/bench start
2025-05-21 16:47:06,446 INFO /usr/local/bin/bench serve --port 8000
2025-05-21 16:47:06,446 INFO /usr/local/bin/bench worker
2025-05-21 16:47:06,460 INFO /usr/local/bin/bench watch
2025-05-21 16:47:06,508 INFO /usr/local/bin/bench schedule
2025-05-21 16:47:24,535 INFO /usr/local/bin/bench --site nexus.com build
2025-05-21 16:55:38,922 INFO /usr/local/bin/bench --site nexus.com build
2025-05-21 16:59:43,292 INFO /usr/local/bin/bench --site nexus.com build
2025-05-21 17:05:30,484 INFO /usr/local/bin/bench start
2025-05-21 17:05:30,741 INFO /usr/local/bin/bench schedule
2025-05-21 17:05:30,789 INFO /usr/local/bin/bench serve --port 8000
2025-05-21 17:05:30,794 INFO /usr/local/bin/bench watch
2025-05-21 17:05:30,810 INFO /usr/local/bin/bench worker
2025-05-21 17:13:20,917 INFO /usr/local/bin/bench start
2025-05-21 17:13:21,165 INFO /usr/local/bin/bench serve --port 8000
2025-05-21 17:13:21,196 INFO /usr/local/bin/bench schedule
2025-05-21 17:13:21,216 INFO /usr/local/bin/bench worker
2025-05-21 17:13:21,216 INFO /usr/local/bin/bench watch
2025-05-21 17:13:25,220 INFO /usr/local/bin/bench --site nexus.com build
2025-05-21 17:20:46,611 INFO /usr/local/bin/bench start
2025-05-21 17:20:46,884 INFO /usr/local/bin/bench schedule
2025-05-21 17:20:46,893 INFO /usr/local/bin/bench watch
2025-05-21 17:20:46,898 INFO /usr/local/bin/bench serve --port 8006
2025-05-21 17:20:46,923 INFO /usr/local/bin/bench worker
2025-05-21 17:20:53,517 INFO /usr/local/bin/bench --site nexus.com build
2025-05-21 17:23:50,704 INFO /usr/local/bin/bench start
2025-05-21 17:23:51,051 INFO /usr/local/bin/bench serve --port 8000
2025-05-21 17:23:51,084 INFO /usr/local/bin/bench schedule
2025-05-21 17:23:51,085 INFO /usr/local/bin/bench worker
2025-05-21 17:23:51,093 INFO /usr/local/bin/bench watch
2025-05-21 17:23:56,654 INFO /usr/local/bin/bench --site nexus.com build
2025-05-21 17:27:14,672 INFO /usr/local/bin/bench --site nexus.com build
2025-05-22 11:46:32,839 INFO /usr/local/bin/bench start
2025-05-22 11:46:33,194 INFO /usr/local/bin/bench serve --port 8000
2025-05-22 11:46:33,204 INFO /usr/local/bin/bench schedule
2025-05-22 11:46:33,205 INFO /usr/local/bin/bench worker
2025-05-22 11:46:33,213 INFO /usr/local/bin/bench watch
2025-05-22 12:00:01,762 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-22 12:04:48,498 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-22 12:07:16,647 INFO /usr/local/bin/bench --site parentngo mariadb
2025-05-22 12:13:02,301 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-22 14:05:10,408 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-22 14:05:41,479 INFO /usr/local/bin/bench --site parentngo mariadb
2025-05-22 14:06:06,465 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-22 14:25:24,786 INFO /usr/local/bin/bench --site parentngo list-doctypes
2025-05-22 14:29:19,523 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-22 14:38:33,666 INFO /usr/local/bin/bench --site parentngo console
2025-05-22 14:40:59,136 INFO /usr/local/bin/bench --site parentngo clear-cache
2025-05-22 14:41:05,423 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-22 14:41:53,082 INFO /usr/local/bin/bench --site parentngo mariadb
2025-05-22 14:42:36,618 INFO /usr/local/bin/bench --site parentngo console
2025-05-22 15:56:13,148 INFO /usr/local/bin/bench --site parentngo export-fixtures
2025-05-22 16:05:04,773 INFO /usr/local/bin/bench --site parentngo console
2025-05-22 16:05:38,949 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-22 16:06:15,200 INFO /usr/local/bin/bench --site parentngo export-fixtures
2025-05-22 16:28:10,333 INFO /usr/local/bin/bench --site parentngo export-fixtures
2025-05-22 16:39:25,059 INFO /usr/local/bin/bench --site parentngo export-fixtures
2025-05-22 18:00:01,383 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-23 16:30:56,416 INFO /usr/local/bin/bench start
2025-05-23 16:30:56,736 INFO /usr/local/bin/bench schedule
2025-05-23 16:30:56,737 INFO /usr/local/bin/bench serve --port 8000
2025-05-23 16:30:56,746 INFO /usr/local/bin/bench worker
2025-05-23 16:30:56,754 INFO /usr/local/bin/bench watch
2025-05-23 16:31:10,688 INFO /usr/local/bin/bench --site nexus.com migrate
2025-05-23 16:36:08,313 INFO /usr/local/bin/bench start
2025-05-23 16:36:08,717 INFO /usr/local/bin/bench worker
2025-05-23 16:36:08,719 INFO /usr/local/bin/bench watch
2025-05-23 16:36:08,731 INFO /usr/local/bin/bench schedule
2025-05-23 16:36:08,739 INFO /usr/local/bin/bench serve --port 8006
2025-05-23 16:39:03,934 INFO /usr/local/bin/bench start
2025-05-23 16:39:04,266 INFO /usr/local/bin/bench serve --port 8006
2025-05-23 16:39:04,276 INFO /usr/local/bin/bench watch
2025-05-23 16:39:04,278 INFO /usr/local/bin/bench worker
2025-05-23 16:39:04,287 INFO /usr/local/bin/bench schedule
2025-05-23 16:41:47,500 INFO /usr/local/bin/bench --site nexus.com build
2025-05-23 18:00:01,945 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-24 00:00:02,085 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-24 06:00:02,037 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-24 12:00:02,259 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-24 15:31:06,329 INFO /usr/local/bin/bench start
2025-05-24 15:31:06,768 INFO /usr/local/bin/bench serve --port 8006
2025-05-24 15:31:06,770 INFO /usr/local/bin/bench schedule
2025-05-24 15:31:06,804 INFO /usr/local/bin/bench worker
2025-05-24 15:31:06,809 INFO /usr/local/bin/bench watch
2025-05-24 15:32:41,753 INFO /usr/local/bin/bench start
2025-05-24 15:32:42,004 INFO /usr/local/bin/bench schedule
2025-05-24 15:32:42,014 INFO /usr/local/bin/bench watch
2025-05-24 15:32:42,067 INFO /usr/local/bin/bench serve --port 8006
2025-05-24 15:32:42,074 INFO /usr/local/bin/bench worker
2025-05-24 15:33:11,239 INFO /usr/local/bin/bench --site child_ngo migrate
2025-05-24 15:35:18,119 INFO /usr/local/bin/bench --site child_ngo set-admin-password root
2025-05-24 16:12:22,615 INFO /usr/local/bin/bench start
2025-05-24 16:12:22,846 INFO /usr/local/bin/bench watch
2025-05-24 16:12:22,852 INFO /usr/local/bin/bench schedule
2025-05-24 16:12:22,855 INFO /usr/local/bin/bench worker
2025-05-24 16:12:22,914 INFO /usr/local/bin/bench serve --port 8006
2025-05-24 16:31:09,985 INFO /usr/local/bin/bench start
2025-05-24 16:31:10,713 INFO /usr/local/bin/bench watch
2025-05-24 16:31:10,830 INFO /usr/local/bin/bench schedule
2025-05-24 16:31:10,889 INFO /usr/local/bin/bench serve --port 8006
2025-05-24 16:31:10,972 INFO /usr/local/bin/bench worker
2025-05-24 18:00:01,903 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-25 11:10:09,744 INFO /usr/local/bin/bench start
2025-05-25 11:10:09,973 INFO /usr/local/bin/bench watch
2025-05-25 11:10:09,986 INFO /usr/local/bin/bench serve --port 8006
2025-05-25 11:10:10,036 INFO /usr/local/bin/bench worker
2025-05-25 11:10:10,043 INFO /usr/local/bin/bench schedule
2025-05-25 12:00:01,717 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-25 12:38:47,051 INFO /usr/local/bin/bench --site nexus.com build
2025-05-25 16:30:42,263 INFO /usr/local/bin/bench --site all console
2025-05-25 17:03:25,630 INFO /usr/local/bin/bench restart
2025-05-25 17:03:25,641 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-05-25 17:03:25,970 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-05-26 00:00:01,314 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-26 06:00:01,800 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-26 10:35:52,055 INFO /usr/local/bin/bench --site all reload-doctype
2025-05-26 11:02:49,413 INFO /usr/local/bin/bench start
2025-05-26 11:02:49,781 INFO /usr/local/bin/bench worker
2025-05-26 11:02:49,803 INFO /usr/local/bin/bench schedule
2025-05-26 11:02:49,810 INFO /usr/local/bin/bench watch
2025-05-26 11:02:49,815 INFO /usr/local/bin/bench serve --port 8006
2025-05-26 11:08:43,856 INFO /usr/local/bin/bench start
2025-05-26 11:09:40,770 INFO /usr/local/bin/bench start
2025-05-26 11:09:41,011 INFO /usr/local/bin/bench schedule
2025-05-26 11:09:41,015 INFO /usr/local/bin/bench worker
2025-05-26 11:09:41,051 INFO /usr/local/bin/bench serve --port 8006
2025-05-26 11:09:41,059 INFO /usr/local/bin/bench watch
2025-05-26 11:24:43,640 INFO /usr/local/bin/bench start
2025-05-26 11:24:43,884 INFO /usr/local/bin/bench worker
2025-05-26 11:24:43,905 INFO /usr/local/bin/bench watch
2025-05-26 11:24:43,942 INFO /usr/local/bin/bench serve --port 8006
2025-05-26 11:24:43,947 INFO /usr/local/bin/bench schedule
2025-05-26 11:57:24,284 INFO /usr/local/bin/bench start
2025-05-26 11:57:24,541 INFO /usr/local/bin/bench watch
2025-05-26 11:57:24,544 INFO /usr/local/bin/bench serve --port 8006
2025-05-26 11:57:24,578 INFO /usr/local/bin/bench worker
2025-05-26 11:57:24,611 INFO /usr/local/bin/bench schedule
2025-05-26 12:00:01,495 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-26 12:26:59,347 INFO /usr/local/bin/bench start
2025-05-26 12:26:59,589 INFO /usr/local/bin/bench watch
2025-05-26 12:26:59,597 INFO /usr/local/bin/bench schedule
2025-05-26 12:26:59,611 INFO /usr/local/bin/bench worker
2025-05-26 12:26:59,639 INFO /usr/local/bin/bench serve --port 8006
2025-05-26 15:47:42,165 INFO /usr/local/bin/bench --site nexus.com build
2025-05-26 16:17:46,130 INFO /usr/local/bin/bench --site nexus.com build
2025-05-26 16:22:13,044 INFO /usr/local/bin/bench --site nexus.com build
2025-05-26 16:30:09,553 INFO /usr/local/bin/bench --site nexus.com build
2025-05-26 16:31:52,493 INFO /usr/local/bin/bench --site nexus.com build
2025-05-26 16:38:54,706 INFO /usr/local/bin/bench start
2025-05-26 16:38:54,959 INFO /usr/local/bin/bench watch
2025-05-26 16:38:54,984 INFO /usr/local/bin/bench schedule
2025-05-26 16:38:55,012 INFO /usr/local/bin/bench serve --port 8006
2025-05-26 16:38:55,023 INFO /usr/local/bin/bench worker
2025-05-26 17:14:45,602 INFO /usr/local/bin/bench --site nexus.com build
2025-05-26 22:32:14,814 INFO /usr/local/bin/bench start
2025-05-26 22:32:15,109 INFO /usr/local/bin/bench schedule
2025-05-26 22:32:15,123 INFO /usr/local/bin/bench worker
2025-05-26 22:32:15,126 INFO /usr/local/bin/bench serve --port 8006
2025-05-26 22:32:15,127 INFO /usr/local/bin/bench watch
2025-05-26 22:32:18,956 INFO /usr/local/bin/bench --site nexus.com build
2025-05-26 23:21:21,886 INFO /usr/local/bin/bench --site nexus.com build
2025-05-27 00:00:02,092 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-27 06:00:01,119 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-27 12:00:01,925 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-27 15:07:23,452 INFO /usr/local/bin/bench start
2025-05-27 15:07:23,738 INFO /usr/local/bin/bench schedule
2025-05-27 15:07:23,738 INFO /usr/local/bin/bench watch
2025-05-27 15:07:23,769 INFO /usr/local/bin/bench serve --port 8006
2025-05-27 15:07:23,772 INFO /usr/local/bin/bench worker
2025-05-27 15:15:17,254 INFO /usr/local/bin/bench --site nexus.com build
2025-05-27 15:27:03,625 INFO /usr/local/bin/bench --site parentngo migrate
2025-05-27 18:00:01,701 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-28 12:00:01,904 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-28 17:25:38,480 INFO /usr/local/bin/bench start
2025-05-28 17:25:38,722 INFO /usr/local/bin/bench serve --port 8006
2025-05-28 17:25:38,724 INFO /usr/local/bin/bench schedule
2025-05-28 17:25:38,760 INFO /usr/local/bin/bench watch
2025-05-28 17:25:38,769 INFO /usr/local/bin/bench worker
2025-05-28 18:00:01,249 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-29 00:00:02,138 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-29 06:00:02,097 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-30 12:00:01,505 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-30 18:00:01,487 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-31 18:00:01,468 INFO /usr/local/bin/bench --verbose --site all backup
2025-05-31 20:52:15,984 INFO /usr/local/bin/bench start
2025-05-31 20:52:16,236 INFO /usr/local/bin/bench serve --port 8006
2025-05-31 20:52:16,246 INFO /usr/local/bin/bench schedule
2025-05-31 20:52:16,247 INFO /usr/local/bin/bench watch
2025-05-31 20:52:16,273 INFO /usr/local/bin/bench worker
2025-06-01 11:20:14,872 INFO /usr/local/bin/bench start
2025-06-01 11:20:15,103 INFO /usr/local/bin/bench serve --port 8006
2025-06-01 11:20:15,131 INFO /usr/local/bin/bench worker
2025-06-01 11:20:15,173 INFO /usr/local/bin/bench watch
2025-06-01 11:20:15,173 INFO /usr/local/bin/bench schedule
2025-06-01 11:20:29,881 INFO /usr/local/bin/bench --site parentngo migrate
2025-06-01 12:00:01,789 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-01 16:04:39,175 INFO /usr/local/bin/bench --site parentngo migrate
2025-06-01 18:00:02,177 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-02 12:00:02,030 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-02 15:32:07,103 INFO /usr/local/bin/bench start
2025-06-02 15:32:07,313 INFO /usr/local/bin/bench schedule
2025-06-02 15:32:07,333 INFO /usr/local/bin/bench watch
2025-06-02 15:32:07,346 INFO /usr/local/bin/bench worker
2025-06-02 15:32:07,350 INFO /usr/local/bin/bench serve --port 8006
2025-06-02 15:44:46,577 INFO /usr/local/bin/bench start
2025-06-02 15:44:46,792 INFO /usr/local/bin/bench serve --port 8006
2025-06-02 15:44:46,813 INFO /usr/local/bin/bench worker
2025-06-02 15:44:46,824 INFO /usr/local/bin/bench watch
2025-06-02 15:44:46,848 INFO /usr/local/bin/bench schedule
2025-06-02 18:00:01,539 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-03 12:00:02,062 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-03 13:46:06,266 INFO /usr/local/bin/bench get-app helpdesk --branch main
2025-06-03 13:46:07,037 LOG Getting helpdesk
2025-06-03 13:46:07,037 DEBUG cd ./apps && git clone https://github.com/frappe/helpdesk.git --branch main --depth 1 --origin upstream
2025-06-03 13:46:17,268 LOG Installing helpdesk
2025-06-03 13:46:17,268 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/helpdesk 
2025-06-03 13:46:28,935 DEBUG cd /home/<USER>/frappe-bench/apps/helpdesk && yarn install --check-files
2025-06-03 13:47:27,368 DEBUG bench build --app helpdesk
2025-06-03 13:47:27,483 INFO /usr/local/bin/bench build --app helpdesk
2025-06-03 13:47:52,636 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-06-03 13:47:52,921 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-03 14:08:19,031 INFO /usr/local/bin/bench new-site brainwise.helpdesk
2025-06-03 14:09:57,301 INFO /usr/local/bin/bench --site brainwise.helpdesk enable-scheduler
2025-06-03 14:10:08,702 INFO /usr/local/bin/bench --site brainwise.helpdesk set-maintenance-mode off
2025-06-03 14:10:27,577 INFO /usr/local/bin/bench --site brainwise.helpdesk set-config server_script_enabled true
2025-06-03 14:10:41,475 INFO /usr/local/bin/bench --site brainwise.helpdesk add-to-hosts
2025-06-03 14:11:16,994 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app iam
2025-06-03 14:11:35,638 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app helpdesk
2025-06-03 14:12:08,201 INFO /usr/local/bin/bench start
2025-06-03 14:12:08,424 INFO /usr/local/bin/bench schedule
2025-06-03 14:12:08,427 INFO /usr/local/bin/bench serve --port 8006
2025-06-03 14:12:08,434 INFO /usr/local/bin/bench worker
2025-06-03 14:12:08,493 INFO /usr/local/bin/bench watch
2025-06-03 14:12:17,867 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app helpdesk
2025-06-03 14:12:32,387 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app migrate
2025-06-03 14:12:41,207 INFO /usr/local/bin/bench --site brainwise.helpdesk migrate
2025-06-03 14:12:55,073 INFO /usr/local/bin/bench --site brainwise.helpdesk build
2025-06-03 18:00:01,558 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-03 18:11:13,653 INFO /usr/local/bin/bench start
2025-06-03 18:11:13,918 INFO /usr/local/bin/bench watch
2025-06-03 18:11:13,926 INFO /usr/local/bin/bench schedule
2025-06-03 18:11:13,936 INFO /usr/local/bin/bench worker
2025-06-03 18:11:13,952 INFO /usr/local/bin/bench serve --port 8006
2025-06-04 12:00:01,630 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-04 12:38:30,870 INFO /usr/local/bin/bench start
2025-06-04 12:38:31,131 INFO /usr/local/bin/bench schedule
2025-06-04 12:38:31,178 INFO /usr/local/bin/bench worker
2025-06-04 12:38:31,183 INFO /usr/local/bin/bench serve --port 8006
2025-06-04 12:38:31,191 INFO /usr/local/bin/bench watch
2025-06-04 12:40:37,893 INFO /usr/local/bin/bench --site brainwise.helpdesk clear-cache
2025-06-04 12:40:43,924 INFO /usr/local/bin/bench --site brainwise.helpdesk clear-website-cache
2025-06-04 12:47:07,048 INFO /usr/local/bin/bench --site brainwise.helpdesk build
2025-06-04 12:50:44,073 INFO /usr/local/bin/bench --site brainwise.helpdesk migrate
2025-06-04 12:50:53,597 INFO /usr/local/bin/bench --site brainwise.helpdesk clear-cache
2025-06-04 12:50:58,104 INFO /usr/local/bin/bench --site brainwise.helpdesk clear-website-cache
2025-06-04 12:55:31,707 INFO /usr/local/bin/bench --site brainwise.helpdesk mariadb
2025-06-04 14:07:15,703 INFO /usr/local/bin/bench --site brainwise.helpdesk uninstall-app helpdesk
2025-06-04 14:07:44,064 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app helpdesk
2025-06-04 14:08:05,040 INFO /usr/local/bin/bench --site brainwise.helpdesk migrate
2025-06-04 14:08:15,037 INFO /usr/local/bin/bench --site brainwise.helpdesk clear-cache
2025-06-04 14:08:18,681 INFO /usr/local/bin/bench --site brainwise.helpdesk clear-website-cache
2025-06-04 14:08:33,012 INFO /usr/local/bin/bench start
2025-06-04 14:08:33,262 INFO /usr/local/bin/bench worker
2025-06-04 14:08:33,265 INFO /usr/local/bin/bench schedule
2025-06-04 14:08:33,268 INFO /usr/local/bin/bench watch
2025-06-04 14:08:33,325 INFO /usr/local/bin/bench serve --port 8006
2025-06-04 15:15:54,598 INFO /usr/local/bin/bench start
2025-06-04 15:15:54,811 INFO /usr/local/bin/bench schedule
2025-06-04 15:15:54,823 INFO /usr/local/bin/bench watch
2025-06-04 15:15:54,874 INFO /usr/local/bin/bench serve --port 8006
2025-06-04 15:15:54,879 INFO /usr/local/bin/bench worker
2025-06-04 16:23:21,158 INFO /usr/local/bin/bench start
2025-06-04 16:23:21,370 INFO /usr/local/bin/bench schedule
2025-06-04 16:23:21,372 INFO /usr/local/bin/bench serve --port 8006
2025-06-04 16:23:21,425 INFO /usr/local/bin/bench watch
2025-06-04 16:23:21,429 INFO /usr/local/bin/bench worker
2025-06-04 16:24:39,193 INFO /usr/local/bin/bench --site brainwise.helpdesk migrate
2025-06-04 16:24:52,629 INFO /usr/local/bin/bench --site brainwise.helpdesk build
2025-06-04 16:59:03,239 INFO /usr/local/bin/bench start
2025-06-04 16:59:03,446 INFO /usr/local/bin/bench serve --port 8006
2025-06-04 16:59:03,500 INFO /usr/local/bin/bench watch
2025-06-04 16:59:03,506 INFO /usr/local/bin/bench schedule
2025-06-04 16:59:03,514 INFO /usr/local/bin/bench worker
2025-06-04 18:00:01,591 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-04 18:03:39,714 INFO /usr/local/bin/bench start
2025-06-04 18:03:40,009 INFO /usr/local/bin/bench schedule
2025-06-04 18:03:40,017 INFO /usr/local/bin/bench worker
2025-06-04 18:03:40,033 INFO /usr/local/bin/bench serve --port 8006
2025-06-04 18:03:40,039 INFO /usr/local/bin/bench watch
2025-06-05 12:00:01,492 INFO /usr/local/bin/bench --verbose --site all backup
2025-06-05 13:51:28,738 INFO /usr/local/bin/bench new-app custom_iam_override
2025-06-05 13:51:28,746 LOG creating new app custom_iam_override
2025-06-05 13:58:24,830 LOG Installing custom_iam_override
2025-06-05 13:58:24,838 DEBUG cd /home/<USER>/frappe-bench && /home/<USER>/frappe-bench/env/bin/python -m pip install --quiet --upgrade -e /home/<USER>/frappe-bench/apps/custom_iam_override 
2025-06-05 13:58:28,084 DEBUG bench build --app custom_iam_override
2025-06-05 13:58:28,257 INFO /usr/local/bin/bench build --app custom_iam_override
2025-06-05 13:58:31,565 WARNING restart failed: Couldn't find supervisorctl in PATH
2025-06-05 13:58:31,965 INFO A newer version of bench is available: 5.23.0 → 5.25.1
2025-06-05 14:20:24,245 INFO /usr/local/bin/bench start
2025-06-05 14:20:24,507 INFO /usr/local/bin/bench serve --port 8006
2025-06-05 14:20:24,531 INFO /usr/local/bin/bench watch
2025-06-05 14:20:24,570 INFO /usr/local/bin/bench schedule
2025-06-05 14:20:24,571 INFO /usr/local/bin/bench worker
2025-06-05 14:23:49,501 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app custom-iam-override
2025-06-05 14:24:04,300 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app custom_iam_override
2025-06-05 14:28:58,833 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app custom_iam_override
2025-06-05 14:31:18,492 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app custom_iam_override
2025-06-05 14:31:30,394 INFO /usr/local/bin/bench --site brainwise.helpdesk uninstall-app custom_iam_override
2025-06-05 14:31:44,676 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app custom_iam_override
2025-06-05 14:31:59,480 INFO /usr/local/bin/bench --site brainwise.helpdesk list-apps
2025-06-05 14:32:18,483 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app custom_iam_override
2025-06-05 14:32:27,280 INFO /usr/local/bin/bench --site brainwise.helpdesk migrate
2025-06-05 14:32:46,859 INFO /usr/local/bin/bench --site brainwise.helpdesk list-apps
2025-06-05 15:19:14,754 INFO /usr/local/bin/bench --site brainwise.helpdesk uninstall-app custom_iam_override
2025-06-05 15:19:24,694 INFO /usr/local/bin/bench --site brainwise.helpdesk install-app custom_iam_override
2025-06-05 15:19:30,614 INFO /usr/local/bin/bench --site brainwise.helpdesk migrate
