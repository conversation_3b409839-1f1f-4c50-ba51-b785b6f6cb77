2025-05-25 12:39:53,889 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
ModuleNotFoundError: No module named 'nexus.api.v1.orders.urls'
2025-05-25 12:39:53,979 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 12:39:54,496 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1722, in call
    fn = get_attr(fn)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1716, in get_attr
    return getattr(get_module(modulename), methodname)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 12, in <module>
    APIS = get_apis()
  File "/home/<USER>/frappe-bench/apps/iam/iam/utils/api.py", line 22, in get_apis
    app_rules = getattr(frappe.get_module(app), "url_patterns")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1434, in get_module
    return importlib.import_module(modulename)
  File "/usr/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/home/<USER>/frappe-bench/apps/nexus/nexus/api/urls.py", line 4, in <module>
    from nexus.api.v1.orders.urls import urlpatterns as orders_urls
ModuleNotFoundError: No module named 'nexus.api.v1.orders.urls'
2025-05-25 12:41:10,396 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'id': 'ORD-005'}
2025-05-25 12:41:53,426 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'id': 'ORD-004'}
2025-05-25 13:02:48,777 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:50,449 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:50,911 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:51,326 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:51,631 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:51,879 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:52,070 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:52,441 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:52,631 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:52,800 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:53,480 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:54,768 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:54,949 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:55,144 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:55,310 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:55,511 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:56,357 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:57,241 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:57,414 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:57,745 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:57,927 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:58,199 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 13:02:58,431 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-25 14:40:27,333 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2'}
2025-05-25 14:43:10,632 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-25 14:43:12,915 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-25 14:54:46,717 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-25 14:56:27,748 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 114, in application
    response = frappe.api.handle(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/api/__init__.py", line 55, in handle
    return build_response("json")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 89, in build_response
    return response_type_map[frappe.response.get("type") or response_type]()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 138, in as_json
    response.data = json.dumps(frappe.local.response, default=json_handler, separators=(",", ":"))
  File "/usr/lib/python3.10/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "/usr/lib/python3.10/json/encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/usr/lib/python3.10/json/encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 239, in json_handler
    raise TypeError(f"""Object of type {type(obj)} with value of {obj!r} is not JSON serializable""")
TypeError: Object of type <class 'nexus.api.v1.orders.schemas.OrdersRetrieved'> with value of <nexus.api.v1.orders.schemas.OrdersRetrieved object at 0x7e12b58931c0> is not JSON serializable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 132, in application
    response = handle_exception(e)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 74, in handle_api_exception
    response = handle_frappe_exception(error)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 334, in handle_exception
    response = frappe.utils.response.report_error(http_status_code)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 58, in report_error
    response = build_response("json")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 89, in build_response
    return response_type_map[frappe.response.get("type") or response_type]()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 138, in as_json
    response.data = json.dumps(frappe.local.response, default=json_handler, separators=(",", ":"))
  File "/usr/lib/python3.10/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "/usr/lib/python3.10/json/encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/usr/lib/python3.10/json/encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 239, in json_handler
    raise TypeError(f"""Object of type {type(obj)} with value of {obj!r} is not JSON serializable""")
TypeError: Object of type <class 'nexus.api.v1.orders.schemas.OrdersRetrieved'> with value of <nexus.api.v1.orders.schemas.OrdersRetrieved object at 0x7e12b58931c0> is not JSON serializable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 50, in handle_response
    prepare_response(response)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 58, in prepare_response
    response.status_code = status_code
AttributeError: 'NoneType' object has no attribute 'status_code'
2025-05-26 11:10:16,840 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 11:10:17,178 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 11:10:17,190 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 11:10:17,232 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 11:11:15,979 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********', 'jwt_only': True, 'cmd': 'iam.api.v1.auth.login'}
2025-05-26 11:16:47,803 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********', 'jwt_only': True, 'cmd': 'iam.api.v1.auth.login'}
2025-05-26 11:25:56,178 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-26 11:58:05,575 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'user': 'Administrator', 'password': '********'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-26 11:58:54,831 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 114, in application
    response = frappe.api.handle(request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/api/__init__.py", line 55, in handle
    return build_response("json")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 89, in build_response
    return response_type_map[frappe.response.get("type") or response_type]()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 138, in as_json
    response.data = json.dumps(frappe.local.response, default=json_handler, separators=(",", ":"))
  File "/usr/lib/python3.10/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "/usr/lib/python3.10/json/encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/usr/lib/python3.10/json/encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 239, in json_handler
    raise TypeError(f"""Object of type {type(obj)} with value of {obj!r} is not JSON serializable""")
TypeError: Object of type <class 'nexus.api.v1.orders.schemas.OrdersRetrieved'> with value of <nexus.api.v1.orders.schemas.OrdersRetrieved object at 0x7d5e7cb93070> is not JSON serializable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 132, in application
    response = handle_exception(e)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 74, in handle_api_exception
    response = handle_frappe_exception(error)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 334, in handle_exception
    response = frappe.utils.response.report_error(http_status_code)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 58, in report_error
    response = build_response("json")
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 89, in build_response
    return response_type_map[frappe.response.get("type") or response_type]()
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 138, in as_json
    response.data = json.dumps(frappe.local.response, default=json_handler, separators=(",", ":"))
  File "/usr/lib/python3.10/json/__init__.py", line 238, in dumps
    **kw).encode(obj)
  File "/usr/lib/python3.10/json/encoder.py", line 199, in encode
    chunks = self.iterencode(o, _one_shot=True)
  File "/usr/lib/python3.10/json/encoder.py", line 257, in iterencode
    return _iterencode(o, 0)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/utils/response.py", line 239, in json_handler
    raise TypeError(f"""Object of type {type(obj)} with value of {obj!r} is not JSON serializable""")
TypeError: Object of type <class 'nexus.api.v1.orders.schemas.OrdersRetrieved'> with value of <nexus.api.v1.orders.schemas.OrdersRetrieved object at 0x7d5e7cb93070> is not JSON serializable

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 50, in handle_response
    prepare_response(response)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 58, in prepare_response
    response.status_code = status_code
AttributeError: 'NoneType' object has no attribute 'status_code'
2025-05-26 12:13:35,588 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 12:14:18,855 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 12:18:42,934 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 12:19:54,794 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'test2', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 12:27:21,326 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'name': 'msm', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 12:45:27,119 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'c', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 13:29:20,530 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'c'}
2025-05-26 13:38:57,413 ERROR frappe Failed to run after request hook
Site: nexus.com
Form Dict: {}
Traceback (most recent call last):
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 146, in application
    run_after_request_hooks(request, response)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/app.py", line 162, in run_after_request_hooks
    frappe.call(after_request_task, response=response, request=request)
  File "/home/<USER>/frappe-bench/apps/frappe/frappe/__init__.py", line 1726, in call
    return fn(*args, **newargs)
  File "/home/<USER>/frappe-bench/apps/iam/iam/app.py", line 49, in handle_response
    if iam.request.is_iam or iam.response.exc_type or iam.response.response_type:
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 318, in __get__
    obj = instance._get_current_object()
  File "/home/<USER>/frappe-bench/env/lib/python3.10/site-packages/werkzeug/local.py", line 501, in _get_current_object
    raise RuntimeError(unbound_message) from None
RuntimeError: object is not bound
2025-05-26 13:39:29,140 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'c', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 13:50:14,473 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'c', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 13:58:32,533 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': '', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 14:16:09,103 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'order_id': 'SAL-ORD-2025-00005', 'cmd': 'nexus.api.v1.orders.endpoints.get_order'}
2025-05-26 14:21:35,020 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'pal', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 14:23:33,302 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'pal', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 14:24:05,403 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'pal', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 14:29:08,497 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'pal', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 14:42:01,457 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'p'}
2025-05-26 14:42:20,293 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 14:42:20,333 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 14:55:24,528 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'search': 'pl', 'cmd': 'nexus.api.v1.orders.endpoints.get_orders'}
2025-05-26 15:42:43,485 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 15:42:43,533 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 15:48:20,407 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 15:48:20,488 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 17:17:02,362 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer': 'CUST-001', 'customer_name': 'John Doe', 'transaction_date': '2025-05-26', 'delivery_date': '2025-05-29', 'status': 'Draft', 'items': [], 'cmd': 'nexus.api.v1.orders.endpoints.create_order'}
2025-05-26 17:29:04,800 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 17:29:04,851 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 22:34:19,187 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {}
2025-05-26 22:34:19,331 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'refresh_token': '********'}
2025-05-26 23:33:05,188 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer': 'Grant Plastics Ltd.', 'customer_name': 'Grant Plastics Ltd.', 'transaction_date': '2025-05-26', 'delivery_date': '2025-05-29', 'status': 'Draft', 'items': [], 'cmd': 'nexus.api.v1.orders.endpoints.create_order'}
2025-05-27 15:20:05,055 ERROR frappe New Exception collected in error log
Site: nexus.com
Form Dict: {'customer': 'Palmer Productions Ltd.', 'customer_name': 'Palmer Productions Ltd.', 'transaction_date': '2025-05-27', 'delivery_date': '2025-05-31', 'status': 'Draft', 'items': [], 'cmd': 'nexus.api.v1.orders.endpoints.create_order'}
2025-06-01 16:15:55,641 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'doctype': 'Loan Request Attachments', 'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-06-01 16:15:56,027 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'docs': '{"name":"Loan Request Attachments","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.446520","modified_by":"Administrator","owner":"Administrator","docstatus":0,"idx":0,"issingle":1,"is_tree":0,"istable":0,"editable_grid":0,"track_changes":0,"module":"Parent Attachments","sort_field":"modified","sort_order":"DESC","read_only":0,"in_create":0,"allow_copy":0,"allow_rename":1,"allow_import":0,"hide_toolbar":0,"track_seen":0,"max_attachments":0,"engine":"InnoDB","is_submittable":0,"show_name_in_global_search":0,"custom":0,"beta":0,"has_web_view":0,"allow_guest_to_view":0,"email_append_to":0,"show_title_field_in_link":0,"migration_hash":"e72c66607e9a757ef1e125b2fbbdf7a3","translated_doctype":0,"is_calendar_and_gantt":0,"quick_entry":0,"track_views":0,"is_virtual":0,"queue_in_background":0,"allow_events_in_timeline":0,"allow_auto_repeat":0,"make_attachments_public":0,"force_re_route_to_default_view":0,"show_preview_popup":0,"index_web_pages_for_search":1,"doctype":"DocType","actions":[],"links":[],"fields":[{"name":"ebm5mh99bp","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.298766","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Loan Request Attachments","parentfield":"fields","parenttype":"DocType","idx":1,"fieldname":"required_attachments","label":"Required Attachments","fieldtype":"Table","options":"Attachment","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"}],"permissions":[{"name":"kblojcat4r","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.298766","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Loan Request Attachments","parentfield":"permissions","parenttype":"DocType","idx":1,"permlevel":0,"role":"System Manager","read":1,"write":1,"create":1,"submit":0,"cancel":0,"delete":1,"amend":0,"report":0,"export":0,"import":0,"share":1,"print":1,"email":1,"if_owner":0,"select":0,"doctype":"DocPerm"},{"name":"al5kpi58c7","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.298766","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Loan Request Attachments","parentfield":"permissions","parenttype":"DocType","idx":2,"permlevel":0,"role":"NGO E-Lending","read":1,"write":1,"create":1,"submit":0,"cancel":0,"delete":1,"amend":0,"report":0,"export":0,"import":0,"share":1,"print":1,"email":1,"if_owner":0,"select":0,"doctype":"DocPerm"}],"states":[],"__last_sync_on":"2025-06-01T13:15:55.596Z"}', 'method': 'check_pending_migration', 'cmd': 'run_doc_method'}
2025-06-01 16:30:19,169 ERROR frappe Error while inserting deferred Error Log record: Error Log uij309k18q: 'Title' (Module import failed for Loan Request Attachments, the DocType you're trying to open might be deleted.
Error: No module named 'parent_ngo.parent_attachments.doctype.loan_request_attachments.loan_request_attachments') will get truncated, as max characters allowed is 140
Site: parentngo
Form Dict: {}
2025-06-01 16:30:19,173 ERROR frappe Error while inserting deferred Error Log record: Error Log st4vr4lmtn: 'Title' ([Errno 2] No such file or directory: '/home/<USER>/frappe-bench/apps/parent_ngo/parent_ngo/parent_attachments/doctype/loan_request_attachments/loan_request_attachments.json') will get truncated, as max characters allowed is 140
Site: parentngo
Form Dict: {}
2025-06-01 16:59:00,101 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'doctype': 'Loan Request Attachments', 'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-06-01 16:59:01,130 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'docs': '{"name":"Loan Request Attachments","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.446520","modified_by":"Administrator","owner":"Administrator","docstatus":0,"idx":0,"issingle":1,"is_tree":0,"istable":0,"editable_grid":0,"track_changes":0,"module":"Parent Attachments","sort_field":"modified","sort_order":"DESC","read_only":0,"in_create":0,"allow_copy":0,"allow_rename":1,"allow_import":0,"hide_toolbar":0,"track_seen":0,"max_attachments":0,"engine":"InnoDB","is_submittable":0,"show_name_in_global_search":0,"custom":0,"beta":0,"has_web_view":0,"allow_guest_to_view":0,"email_append_to":0,"show_title_field_in_link":0,"migration_hash":"e72c66607e9a757ef1e125b2fbbdf7a3","translated_doctype":0,"is_calendar_and_gantt":0,"quick_entry":0,"track_views":0,"is_virtual":0,"queue_in_background":0,"allow_events_in_timeline":0,"allow_auto_repeat":0,"make_attachments_public":0,"force_re_route_to_default_view":0,"show_preview_popup":0,"index_web_pages_for_search":1,"doctype":"DocType","actions":[],"links":[],"fields":[{"name":"ebm5mh99bp","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.298766","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Loan Request Attachments","parentfield":"fields","parenttype":"DocType","idx":1,"fieldname":"required_attachments","label":"Required Attachments","fieldtype":"Table","options":"Attachment","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"}],"permissions":[{"name":"kblojcat4r","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.298766","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Loan Request Attachments","parentfield":"permissions","parenttype":"DocType","idx":1,"permlevel":0,"role":"System Manager","read":1,"write":1,"create":1,"submit":0,"cancel":0,"delete":1,"amend":0,"report":0,"export":0,"import":0,"share":1,"print":1,"email":1,"if_owner":0,"select":0,"doctype":"DocPerm"},{"name":"al5kpi58c7","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.298766","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Loan Request Attachments","parentfield":"permissions","parenttype":"DocType","idx":2,"permlevel":0,"role":"NGO E-Lending","read":1,"write":1,"create":1,"submit":0,"cancel":0,"delete":1,"amend":0,"report":0,"export":0,"import":0,"share":1,"print":1,"email":1,"if_owner":0,"select":0,"doctype":"DocPerm"}],"states":[],"__last_sync_on":"2025-06-01T13:59:00.838Z"}', 'method': 'check_pending_migration', 'cmd': 'run_doc_method'}
2025-06-01 17:00:47,538 ERROR frappe Error while inserting deferred Error Log record: Error Log 1sst6iatfr: 'Title' (Module import failed for Loan Request Attachments, the DocType you're trying to open might be deleted.
Error: No module named 'parent_ngo.parent_attachments.doctype.loan_request_attachments.loan_request_attachments') will get truncated, as max characters allowed is 140
Site: parentngo
Form Dict: {}
2025-06-01 17:00:47,544 ERROR frappe Error while inserting deferred Error Log record: Error Log 3jd97ilgef: 'Title' ([Errno 2] No such file or directory: '/home/<USER>/frappe-bench/apps/parent_ngo/parent_ngo/parent_attachments/doctype/loan_request_attachments/loan_request_attachments.json') will get truncated, as max characters allowed is 140
Site: parentngo
Form Dict: {}
2025-06-01 17:05:27,271 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'doctype': 'Loan Request Attachments', 'with_parent': '1', 'cached_timestamp': '', 'cmd': 'frappe.desk.form.load.getdoctype'}
2025-06-01 17:05:28,399 ERROR frappe New Exception collected in error log
Site: parentngo
Form Dict: {'docs': '{"name":"Loan Request Attachments","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.446520","modified_by":"Administrator","owner":"Administrator","docstatus":0,"idx":0,"issingle":1,"is_tree":0,"istable":0,"editable_grid":0,"track_changes":0,"module":"Parent Attachments","sort_field":"modified","sort_order":"DESC","read_only":0,"in_create":0,"allow_copy":0,"allow_rename":1,"allow_import":0,"hide_toolbar":0,"track_seen":0,"max_attachments":0,"engine":"InnoDB","is_submittable":0,"show_name_in_global_search":0,"custom":0,"beta":0,"has_web_view":0,"allow_guest_to_view":0,"email_append_to":0,"show_title_field_in_link":0,"migration_hash":"e72c66607e9a757ef1e125b2fbbdf7a3","translated_doctype":0,"is_calendar_and_gantt":0,"quick_entry":0,"track_views":0,"is_virtual":0,"queue_in_background":0,"allow_events_in_timeline":0,"allow_auto_repeat":0,"make_attachments_public":0,"force_re_route_to_default_view":0,"show_preview_popup":0,"index_web_pages_for_search":1,"doctype":"DocType","actions":[],"links":[],"fields":[{"name":"ebm5mh99bp","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.298766","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Loan Request Attachments","parentfield":"fields","parenttype":"DocType","idx":1,"fieldname":"required_attachments","label":"Required Attachments","fieldtype":"Table","options":"Attachment","search_index":0,"show_dashboard":0,"hidden":0,"set_only_once":0,"allow_in_quick_entry":0,"print_hide":0,"report_hide":0,"reqd":0,"bold":0,"in_global_search":0,"collapsible":0,"unique":0,"no_copy":0,"allow_on_submit":0,"permlevel":0,"ignore_user_permissions":0,"columns":0,"in_list_view":0,"fetch_if_empty":0,"in_filter":0,"remember_last_selected_value":0,"ignore_xss_filter":0,"print_hide_if_no_value":0,"allow_bulk_edit":0,"in_standard_filter":0,"in_preview":0,"read_only":0,"length":0,"translatable":0,"hide_border":0,"hide_days":0,"hide_seconds":0,"non_negative":0,"is_virtual":0,"sort_options":0,"show_on_timeline":0,"make_attachment_public":0,"doctype":"DocField"}],"permissions":[{"name":"kblojcat4r","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.298766","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Loan Request Attachments","parentfield":"permissions","parenttype":"DocType","idx":1,"permlevel":0,"role":"System Manager","read":1,"write":1,"create":1,"submit":0,"cancel":0,"delete":1,"amend":0,"report":0,"export":0,"import":0,"share":1,"print":1,"email":1,"if_owner":0,"select":0,"doctype":"DocPerm"},{"name":"al5kpi58c7","creation":"2025-05-22 15:19:02.149434","modified":"2025-06-01 16:04:58.298766","modified_by":"Administrator","owner":"Administrator","docstatus":0,"parent":"Loan Request Attachments","parentfield":"permissions","parenttype":"DocType","idx":2,"permlevel":0,"role":"NGO E-Lending","read":1,"write":1,"create":1,"submit":0,"cancel":0,"delete":1,"amend":0,"report":0,"export":0,"import":0,"share":1,"print":1,"email":1,"if_owner":0,"select":0,"doctype":"DocPerm"}],"states":[],"__last_sync_on":"2025-06-01T14:05:28.125Z"}', 'method': 'check_pending_migration', 'cmd': 'run_doc_method'}
2025-06-01 17:15:17,134 ERROR frappe Error while inserting deferred Error Log record: Error Log i3lpeg0hf6: 'Title' (Module import failed for Loan Request Attachments, the DocType you're trying to open might be deleted.
Error: No module named 'parent_ngo.parent_attachments.doctype.loan_request_attachments.loan_request_attachments') will get truncated, as max characters allowed is 140
Site: parentngo
Form Dict: {}
2025-06-01 17:15:17,145 ERROR frappe Error while inserting deferred Error Log record: Error Log d5egpivfrp: 'Title' ([Errno 2] No such file or directory: '/home/<USER>/frappe-bench/apps/parent_ngo/parent_ngo/parent_attachments/doctype/loan_request_attachments/loan_request_attachments.json') will get truncated, as max characters allowed is 140
Site: parentngo
Form Dict: {}
2025-06-04 14:09:08,331 ERROR frappe New Exception collected in error log
Site: brainwise.helpdesk
Form Dict: {'doc': '{"docstatus":0,"doctype":"HD Ticket","name":"new-hd-ticket-prufcdcwbp","__islocal":1,"__unsaved":1,"owner":"Administrator","status":"Open","agreement_status":"","opening_date":"2025-06-04","via_customer_portal":0,"is_merged":0,"__run_link_triggers":1,"subject":"sss"}', 'cmd': 'frappe.client.save'}
2025-06-04 14:54:42,969 ERROR frappe New Exception collected in error log
Site: brainwise.helpdesk
Form Dict: {'doc': '{"docstatus":0,"doctype":"HD Ticket","name":"new-hd-ticket-mqqcwtiscd","__islocal":1,"__unsaved":1,"owner":"Administrator","status":"Open","agreement_status":"","opening_date":"2025-06-04","via_customer_portal":0,"is_merged":0,"__run_link_triggers":1,"subject":"برشس"}', 'cmd': 'frappe.client.save'}
2025-06-04 14:58:27,653 ERROR frappe New Exception collected in error log
Site: brainwise.helpdesk
Form Dict: {'doc': '{"docstatus":0,"doctype":"HD Ticket","name":"new-hd-ticket-amwrddygtq","__islocal":1,"__unsaved":1,"owner":"Administrator","status":"Open","agreement_status":"","opening_date":"2025-06-04","via_customer_portal":0,"is_merged":0,"__run_link_triggers":1,"subject":"njn"}', 'cmd': 'frappe.client.save'}
2025-06-04 15:00:23,344 ERROR frappe New Exception collected in error log
Site: brainwise.helpdesk
Form Dict: {'doc': '{"docstatus":0,"doctype":"HD Ticket","name":"new-hd-ticket-amwrddygtq","__islocal":1,"__unsaved":1,"owner":"Administrator","status":"Open","agreement_status":"","opening_date":"2025-06-04","via_customer_portal":0,"is_merged":0,"__run_link_triggers":1,"subject":"njn"}', 'cmd': 'frappe.client.save'}
2025-06-04 15:01:15,250 ERROR frappe New Exception collected in error log
Site: brainwise.helpdesk
Form Dict: {'doc': '{"docstatus":0,"doctype":"HD Ticket","name":"new-hd-ticket-amwrddygtq","__islocal":1,"__unsaved":1,"owner":"Administrator","status":"Open","agreement_status":"","opening_date":"2025-06-04","via_customer_portal":0,"is_merged":0,"__run_link_triggers":false,"subject":"njn"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
2025-06-04 15:03:01,667 ERROR frappe New Exception collected in error log
Site: brainwise.helpdesk
Form Dict: {'doc': '{"docstatus":0,"doctype":"HD Ticket","name":"new-hd-ticket-amwrddygtq","__islocal":1,"__unsaved":1,"owner":"Administrator","status":"Open","agreement_status":"","opening_date":"2025-06-04","via_customer_portal":0,"is_merged":0,"__run_link_triggers":false,"subject":"njn"}', 'action': 'Save', 'cmd': 'frappe.desk.form.save.savedocs'}
