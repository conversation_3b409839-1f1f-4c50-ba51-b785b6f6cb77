{"actions": [], "autoname": "field:service_level", "creation": "2023-08-22 11:14:25.683372", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["default_priority", "column_break_2", "service_level", "enabled", "filters_section", "default_sla", "column_break_15", "condition", "agreement_details_section", "start_date", "column_break_7", "end_date", "response_and_resolution_time_section", "apply_sla_for_resolution", "priorities", "status_details", "sla_fulfilled_on", "column_break_22", "pause_sla_on", "support_and_resolution_section_break", "holiday_list", "support_and_resolution"], "fields": [{"fieldname": "service_level", "fieldtype": "Data", "in_list_view": 1, "in_standard_filter": 1, "label": "Service Level Name", "reqd": 1, "set_only_once": 1, "unique": 1}, {"fieldname": "holiday_list", "fieldtype": "Link", "label": "Holiday List", "options": "HD Service Holiday List", "reqd": 1}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"fieldname": "agreement_details_section", "fieldtype": "Section Break", "label": "<PERSON><PERSON>"}, {"fieldname": "start_date", "fieldtype": "Date", "label": "Start Date"}, {"fieldname": "column_break_7", "fieldtype": "Column Break"}, {"fieldname": "end_date", "fieldtype": "Date", "label": "End Date"}, {"fieldname": "response_and_resolution_time_section", "fieldtype": "Section Break", "label": "Response and Resolution"}, {"fieldname": "support_and_resolution_section_break", "fieldtype": "Section Break", "label": "Working Hours"}, {"fieldname": "support_and_resolution", "fieldtype": "Table", "label": "Working Hours", "options": "HD Service Day", "reqd": 1}, {"fieldname": "priorities", "fieldtype": "Table", "label": "Priorities", "options": "HD Service Level Priority", "reqd": 1}, {"default": "0", "fieldname": "default_sla", "fieldtype": "Check", "label": "Default SLA"}, {"fieldname": "default_priority", "fieldtype": "Link", "label": "Default Priority", "options": "HD Ticket Priority", "read_only": 1}, {"fieldname": "pause_sla_on", "fieldtype": "Table", "label": "SLA Paused On", "options": "HD Pause Service Level Agreement On Status"}, {"default": "1", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}, {"fieldname": "status_details", "fieldtype": "Section Break", "label": "Status Details"}, {"fieldname": "sla_fulfilled_on", "fieldtype": "Table", "label": "SLA Fulfilled On", "options": "HD Service Level Agreement Fulfilled On Status", "reqd": 1}, {"default": "1", "fieldname": "apply_sla_for_resolution", "fieldtype": "Check", "label": "Apply SLA for Resolution Time"}, {"fieldname": "filters_section", "fieldtype": "Section Break", "label": "Assignment Conditions"}, {"fieldname": "column_break_15", "fieldtype": "Column Break"}, {"depends_on": "eval: !doc.default_sla", "description": "Simple Python Expression, Example: doc.status == 'Open' and doc.ticket_type == 'Bug'", "fieldname": "condition", "fieldtype": "Code", "label": "Condition", "max_height": "7rem", "options": "PythonExpression"}, {"fieldname": "column_break_22", "fieldtype": "Column Break"}], "links": [], "modified": "2023-08-31 13:43:18.219434", "modified_by": "Administrator", "module": "Helpdesk", "name": "HD Service Level Agreement", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Agent", "share": 1, "write": 1}, {"email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "All", "share": 1}], "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}