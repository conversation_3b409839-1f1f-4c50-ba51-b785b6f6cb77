{% macro button(label, link, primary = true) %}
<table cellpadding="0" cellspacing="0" role="presentation">
  <tbody>
    <tr>
      <td class="button {{ 'button-primary' if primary else '' }}">
        <a href="{{ link }}" rel="nofollow"> {{ label }} </a>
      </td>
    </tr>
  </tbody>
</table>
{% endmacro %} {% macro link(label, link) %}
<a class="link" href="{{ link }}">{{ label }}</a>
{% endmacro %} {% macro separator() %}
<table class="w-full" cellpadding="0" cellspacing="0" role="presentation">
  <tbody>
    <tr>
      <td class="py-8">
        <div class="leading-px h-px bg-gray-300"></div>
      </td>
    </tr>
  </tbody>
</table>
{% endmacro %} {% macro signature() %}
<p class="m-0">
  Thanks,<br />
  Team Frappe
</p>
{% endmacro %}
