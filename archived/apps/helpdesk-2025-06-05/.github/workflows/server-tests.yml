name: Server

on:
  pull_request:
    branches:
      - main
      - develop

    paths-ignore:
      - '**.js'
      - '**.css'
      - '**.md'
      - '**.html'
      - '**.csv'
  workflow_dispatch:
    inputs:
      user:
        description: 'Frappe Framework repository user (add your username for forks)'
        required: true
        default: 'frappe'
        type: string
      branch:
        description: 'Frappe Framework branch'
        default: 'develop'
        required: false
        type: string

concurrency:
  group: develop-${{ github.event.number }}
  cancel-in-progress: true

jobs:
  test:
    name: Python Unit Tests
    runs-on: ubuntu-latest
    timeout-minutes: 60

    strategy:
      fail-fast: false

    services:
        mysql:
          image: mariadb:10.6
          env:
            MARIADB_ROOT_PASSWORD: 'root'
          ports:
            - 3306:3306
          options: --health-cmd="mariadb-admin ping" --health-interval=5s --health-timeout=2s --health-retries=3

    steps:
      - name: C<PERSON>
        uses: actions/checkout@v2

      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: '3.10'

      - name: Check for valid Python & Merge Conflicts
        run: |
          python -m compileall -f "${GITHUB_WORKSPACE}"
          if grep -lr --exclude-dir=node_modules "^<<<<<<< " "${GITHUB_WORKSPACE}"
              then echo "Found merge conflicts"
              exit 1
          fi

      - name: Setup Node
        uses: actions/setup-node@v2
        with:
          node-version: 18
          check-latest: true

      - name: Install Yarn
        run: npm install -g yarn

      - name: Add to Hosts
        run: echo "127.0.0.1 test_site" | sudo tee -a /etc/hosts

      - name: Cache pip
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/*requirements.txt', '**/pyproject.toml') }}
          restore-keys: |
            ${{ runner.os }}-pip-
            ${{ runner.os }}-

      - name: Cache node modules
        uses: actions/cache@v4
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v4
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install
        run: bash ${GITHUB_WORKSPACE}/.github/helpers/install.sh
        env:
          TYPE: server
          FRAPPE_USER: ${{ github.event.inputs.user }}
          FRAPPE_BRANCH: ${{ github.event.inputs.branch }}

      # - name: Run Tests
      #   run: 'cd ~/frappe-bench/ && bench --site test_site run-tests --app helpdesk'
      #   env:
      #     TYPE: server
      #     CI_BUILD_ID: ${{ github.run_id }}
      #     ORCHESTRATOR_URL: http://test-orchestrator.frappe.io

