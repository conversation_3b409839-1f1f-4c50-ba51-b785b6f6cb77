Metadata-Version: 2.4
Name: custom_iam_override
Version: 0.0.1
Summary: Custom app to override IAM file handling functions
Author-email: Your Organization <<EMAIL>>
Requires-Python: >=3.10
Description-Content-Type: text/markdown

# Custom IAM Override

A Frappe app that overrides the `set_file_details` function from the IAM app without modifying the original IAM code.

## Installing App

1. Install the App to the bench from the Repository:

```bash
bench get-app --branch main custom_iam_override **************:BrainWise-DEV/custom_iam_override.git
```

2. Install the App to the site:

```bash
bench --site [site-name] install-app custom_iam_override
```

3. Migrate the changes:

```bash
bench --site [site-name] migrate
```

4. Build the App:

```bash
bench --site [site-name] build
```

