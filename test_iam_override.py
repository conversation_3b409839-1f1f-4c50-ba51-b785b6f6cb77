#!/usr/bin/env python3
"""
Test script to verify IAM override functionality.
Run this from the frappe-bench directory.

Usage:
    python test_iam_override.py
"""

import frappe


def test_override():
    """Test the IAM override functionality"""
    try:
        # Initialize Frappe
        frappe.init(site="your-site-name")  # Replace with your actual site name
        frappe.connect()
        
        print("🔄 Testing IAM override functionality...")
        
        # Apply the overrides
        from custom_iam_override.overrides.iam_file_utils import apply_iam_overrides
        apply_iam_overrides()
        
        # Test that the function has been overridden
        import iam.utils.file as iam_file_utils
        
        # Check if the function has been overridden
        if hasattr(iam_file_utils, '_original_set_file_details'):
            print("✅ Successfully applied IAM file utility overrides!")
            print("📝 Original functions have been backed up")
            print("🔧 Custom set_file_details function is now active")
        else:
            print("❌ Override may not have been applied correctly")
        
        # Test the function with a mock file object
        class MockFile:
            def __init__(self):
                self.is_folder = False
                self.from_api = False
                self.is_details_set = False
                self.attached_to_doctype = "Test Doctype"
                self.attached_to_name = "TEST-001"
                self.file_name = "test_file.pdf"
                self.name_hash = None
                
            def get(self, key):
                return getattr(self, key, None)
        
        mock_file = MockFile()
        
        try:
            iam_file_utils.set_file_details(mock_file)
            print("✅ Custom set_file_details function executed successfully!")
            print(f"📁 Generated file name: {mock_file.file_name}")
            print(f"🔗 Name hash: {mock_file.name_hash}")
        except Exception as e:
            print(f"⚠️  Function executed but with error: {str(e)}")
            print("This might be expected if dependencies are missing")
        
        print("\n🎉 IAM override test completed!")
        
    except ImportError as e:
        print(f"❌ Could not import custom_iam_override: {str(e)}")
        print("Make sure the custom_iam_override app is installed")
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
    finally:
        frappe.destroy()


if __name__ == "__main__":
    test_override()
