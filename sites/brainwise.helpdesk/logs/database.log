2025-06-03 14:11:39,597 WARNING database DDL Query made to DB:
create table `tabHD Escalation Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_enabled` int(1) not null default 0,
`priority` varchar(140),
`team` varchar(140),
`ticket_type` varchar(140),
`to_agent` varchar(140),
`to_team` varchar(140),
`to_priority` varchar(140),
`to_ticket_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:11:39,686 WARNING database DDL Query made to DB:
create table `tabHD Canned Response` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`message` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:11:39,781 WARNING database DDL Query made to DB:
create table `tabHD Service Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workday` varchar(140),
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:11:39,854 WARNING database DDL Query made to DB:
create sequence if not exists hd_team_member_id_seq nocache nocycle
2025-06-03 14:11:39,871 WARNING database DDL Query made to DB:
create table `tabHD Team Member` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:11:39,950 WARNING database DDL Query made to DB:
create sequence if not exists hd_preset_filter_item_id_seq nocache nocycle
2025-06-03 14:11:39,970 WARNING database DDL Query made to DB:
create table `tabHD Preset Filter Item` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`fieldname` varchar(140),
`filter_type` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:11:40,070 WARNING database DDL Query made to DB:
create table `tabHD View` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`icon` varchar(140),
`user` varchar(140),
`is_default` int(1) not null default 0,
`is_customer_portal` int(1) not null default 0,
`type` varchar(140) default 'list',
`dt` varchar(140),
`route_name` varchar(140),
`pinned` int(1) not null default 0,
`public` int(1) not null default 0,
`filters` longtext,
`order_by` longtext,
`load_default_columns` int(1) not null default 0,
`columns` longtext,
`rows` longtext,
`group_by_field` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:11:40,184 WARNING database DDL Query made to DB:
create table `tabHD Preset Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`reference_doctype` varchar(140),
`type` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:11:40,292 WARNING database DDL Query made to DB:
create table `tabHD Support Search Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140),
`source_type` varchar(140),
`base_url` varchar(140),
`query_route` varchar(140),
`search_term_param_name` varchar(140),
`response_result_key_path` varchar(140),
`post_route` varchar(140),
`post_route_key_list` varchar(140),
`post_title_key` varchar(140),
`post_description_key` varchar(140),
`source_doctype` varchar(140),
`result_title_field` varchar(140),
`result_preview_field` varchar(140),
`result_route_field` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:11:40,386 WARNING database DDL Query made to DB:
create table `tabHD Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_enabled` int(1) not null default 0,
`button_label` varchar(140),
`button_icon` varchar(140),
`is_external_link` int(1) not null default 0,
`action` longtext,
`cond_hidden` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:11:40,491 WARNING database DDL Query made to DB:
create table `tabHD Agent` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`agent_name` varchar(140),
`user_image` varchar(140),
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:11:40,598 WARNING database DDL Query made to DB:
create table `tabHD Ticket Feedback Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`rating` decimal(3,2),
`label` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:11:40,704 WARNING database DDL Query made to DB:
create table `tabHD Desk Account Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_key` varchar(140),
`ip_address` varchar(140),
`email` varchar(140) unique,
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-03 14:12:41,997 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-04 12:50:45,278 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-04 14:07:22,383 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Ticket Type`
2025-06-04 14:07:22,454 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD View`
2025-06-04 14:07:22,497 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Ticket Comment`
2025-06-04 14:07:22,540 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Portal Signup Request`
2025-06-04 14:07:22,584 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Team Member`
2025-06-04 14:07:22,623 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Agent`
2025-06-04 14:07:22,665 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Preset Filter`
2025-06-04 14:07:22,713 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Escalation Rule`
2025-06-04 14:07:22,752 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Ticket Template`
2025-06-04 14:07:22,795 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Preset Filter Item`
2025-06-04 14:07:22,857 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Support Search Source`
2025-06-04 14:07:22,897 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Stopword`
2025-06-04 14:07:22,935 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Ticket Template Field`
2025-06-04 14:07:22,968 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Synonym`
2025-06-04 14:07:23,033 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Service Level Agreement`
2025-06-04 14:07:23,078 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Ticket Activity`
2025-06-04 14:07:23,116 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Desk Account Request`
2025-06-04 14:07:23,155 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Organization Contact Item`
2025-06-04 14:07:23,188 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Ticket Priority`
2025-06-04 14:07:23,229 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Article Category`
2025-06-04 14:07:23,279 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Action`
2025-06-04 14:07:23,317 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Article`
2025-06-04 14:07:23,351 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Customer`
2025-06-04 14:07:23,407 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Ticket`
2025-06-04 14:07:23,440 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Organization`
2025-06-04 14:07:23,509 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Pause Service Level Agreement On Status`
2025-06-04 14:07:23,541 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Service Day`
2025-06-04 14:07:23,579 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Article Feedback`
2025-06-04 14:07:23,614 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Ticket Feedback Option`
2025-06-04 14:07:23,648 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Holiday`
2025-06-04 14:07:23,684 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Notification`
2025-06-04 14:07:23,725 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Service Holiday List`
2025-06-04 14:07:23,758 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Canned Response`
2025-06-04 14:07:23,790 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Service Level Agreement Fulfilled On Status`
2025-06-04 14:07:23,828 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Form Script`
2025-06-04 14:07:23,866 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Service Level Priority`
2025-06-04 14:07:23,901 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Synonyms`
2025-06-04 14:07:23,939 WARNING database DDL Query made to DB:
DROP TABLE IF EXISTS `tabHD Team`
2025-06-04 14:07:45,293 WARNING database DDL Query made to DB:
create table `tabHD Ticket Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`template_name` varchar(140) unique,
`about` longtext,
`description_template` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:45,429 WARNING database DDL Query made to DB:
create table `tabHD Team` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`team_name` varchar(140) unique,
`assignment_rule` varchar(140),
`ignore_restrictions` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:45,526 WARNING database DDL Query made to DB:
create table `tabHD Holiday` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`holiday_date` date,
`weekly_off` int(1) not null default 0,
`description` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:45,621 WARNING database DDL Query made to DB:
create table `tabHD Ticket Comment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_ticket` varchar(140),
`commented_by` varchar(140),
`is_pinned` int(1) not null default 0,
`content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `reference_ticket`(`reference_ticket`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:45,720 WARNING database DDL Query made to DB:
create table `tabHD Article Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category_name` varchar(140),
`description` text,
`icon` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:45,830 WARNING database DDL Query made to DB:
create table `tabHD Notification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`read` int(1) not null default 0,
`user_from` varchar(140),
`user_to` varchar(140),
`notification_type` varchar(140),
`reference_ticket` varchar(140),
`reference_comment` varchar(140),
`message` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:45,935 WARNING database DDL Query made to DB:
create table `tabHD Ticket Template Field` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fieldname` varchar(140),
`url_method` varchar(140),
`required` int(1) not null default 0,
`hide_from_customer` int(1) not null default 0,
`placeholder` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:46,020 WARNING database DDL Query made to DB:
create table `tabHD Portal Signup Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_key` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:46,114 WARNING database DDL Query made to DB:
create table `tabHD Ticket Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_system` int(1) not null default 0,
`description` text,
`priority` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:46,271 WARNING database DDL Query made to DB:
create sequence if not exists hd_ticket_id_seq nocache nocycle
2025-06-04 14:07:46,293 WARNING database DDL Query made to DB:
create table `tabHD Ticket` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`subject` varchar(140),
`raised_by` varchar(140),
`status` varchar(140) default 'Open',
`priority` varchar(140),
`ticket_type` varchar(140),
`agent_group` varchar(140),
`summary` longtext,
`description` longtext,
`template` varchar(140),
`sla` varchar(140),
`response_by` datetime(6),
`agreement_status` varchar(140),
`resolution_by` datetime(6),
`service_level_agreement_creation` datetime(6),
`on_hold_since` datetime(6),
`total_hold_time` decimal(21,9),
`first_response_time` decimal(21,9),
`first_responded_on` datetime(6),
`avg_response_time` decimal(21,9),
`resolution_details` longtext,
`opening_date` date,
`opening_time` time(6),
`resolution_date` datetime(6),
`resolution_time` decimal(21,9),
`user_resolution_time` decimal(21,9),
`contact` varchar(140),
`customer` varchar(140),
`email_account` varchar(140),
`via_customer_portal` int(1) not null default 0,
`attachment` text,
`content_type` varchar(140),
`is_merged` int(1) not null default 0,
`merged_with` varchar(140),
`ticket_split_from` varchar(140),
`feedback_rating` decimal(3,2),
`feedback_text` varchar(140),
`feedback` varchar(140),
`feedback_extra` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `status`(`status`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:46,405 WARNING database DDL Query made to DB:
create table `tabHD Ticket Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`integer_value` int(11) not null default 0,
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:46,515 WARNING database DDL Query made to DB:
create table `tabHD Service Level Priority` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_priority` int(1) not null default 0,
`priority` varchar(140),
`response_time` decimal(21,9),
`resolution_time` decimal(21,9),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:46,618 WARNING database DDL Query made to DB:
create table `tabHD Article` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`status` varchar(140) default 'Draft',
`published_on` datetime(6),
`views` int(11) not null default 0,
`category` varchar(140),
`author` varchar(140),
`content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:46,716 WARNING database DDL Query made to DB:
create table `tabHD Service Level Agreement Fulfilled On Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:46,831 WARNING database DDL Query made to DB:
create table `tabHD Service Level Agreement` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`default_priority` varchar(140),
`service_level` varchar(140) unique,
`enabled` int(1) not null default 1,
`default_sla` int(1) not null default 0,
`condition` longtext,
`start_date` date,
`end_date` date,
`apply_sla_for_resolution` int(1) not null default 1,
`holiday_list` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:46,934 WARNING database DDL Query made to DB:
create table `tabHD Stopword` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`word` varchar(140) unique,
`enabled` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:47,027 WARNING database DDL Query made to DB:
create table `tabHD Synonym` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`synonym` varchar(140) unique,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:47,118 WARNING database DDL Query made to DB:
create table `tabHD Customer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`image` text,
`customer_name` varchar(140) unique,
`domain` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:47,225 WARNING database DDL Query made to DB:
create table `tabHD Form Script` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`dt` varchar(140) default 'HD Ticket',
`apply_to` varchar(140) default 'Form',
`enabled` int(1) not null default 0,
`apply_to_customer_portal` int(1) not null default 0,
`apply_on_new_page` int(1) not null default 0,
`script` longtext default 'function setupForm({doc, updateField, call, router, $dialog, createToast ,applyFilters}) {\n    return {\n        actions: [],\n        onChange:{\n//          works only for new ticket page\n            \"fieldname\":(newVal)=>console.log(newVal)\n        }\n    }\n}',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:47,318 WARNING database DDL Query made to DB:
create table `tabHD Article Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`article` varchar(140),
`user` varchar(140),
`feedback` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:47,415 WARNING database DDL Query made to DB:
create table `tabHD Synonyms` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`word` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:47,504 WARNING database DDL Query made to DB:
create table `tabHD Organization Contact Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`contact` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:47,714 WARNING database DDL Query made to DB:
create table `tabHD Ticket Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`ticket` varchar(140),
`action` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `ticket`(`ticket`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:47,809 WARNING database DDL Query made to DB:
create table `tabHD Pause Service Level Agreement On Status` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:47,903 WARNING database DDL Query made to DB:
create table `tabHD Organization` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`organization_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:48,030 WARNING database DDL Query made to DB:
create table `tabHD Service Holiday List` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`holiday_list_name` varchar(140) unique,
`from_date` date,
`to_date` date,
`total_holidays` int(11) not null default 0,
`weekly_off` varchar(140),
`color` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:48,162 WARNING database DDL Query made to DB:
create table `tabHD Escalation Rule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_enabled` int(1) not null default 0,
`priority` varchar(140),
`team` varchar(140),
`ticket_type` varchar(140),
`to_agent` varchar(140),
`to_team` varchar(140),
`to_priority` varchar(140),
`to_ticket_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:48,263 WARNING database DDL Query made to DB:
create table `tabHD Canned Response` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`message` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:48,389 WARNING database DDL Query made to DB:
create table `tabHD Service Day` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`workday` varchar(140),
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:48,490 WARNING database DDL Query made to DB:
create sequence if not exists hd_team_member_id_seq nocache nocycle
2025-06-04 14:07:48,507 WARNING database DDL Query made to DB:
create table `tabHD Team Member` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:48,627 WARNING database DDL Query made to DB:
create sequence if not exists hd_preset_filter_item_id_seq nocache nocycle
2025-06-04 14:07:48,643 WARNING database DDL Query made to DB:
create table `tabHD Preset Filter Item` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`fieldname` varchar(140),
`filter_type` varchar(140),
`value` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:48,802 WARNING database DDL Query made to DB:
create table `tabHD View` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`label` varchar(140),
`icon` varchar(140),
`user` varchar(140),
`is_default` int(1) not null default 0,
`is_customer_portal` int(1) not null default 0,
`type` varchar(140) default 'list',
`dt` varchar(140),
`route_name` varchar(140),
`pinned` int(1) not null default 0,
`public` int(1) not null default 0,
`filters` longtext,
`order_by` longtext,
`load_default_columns` int(1) not null default 0,
`columns` longtext,
`rows` longtext,
`group_by_field` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:48,944 WARNING database DDL Query made to DB:
create table `tabHD Preset Filter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`reference_doctype` varchar(140),
`type` varchar(140),
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:49,077 WARNING database DDL Query made to DB:
create table `tabHD Support Search Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source_name` varchar(140),
`source_type` varchar(140),
`base_url` varchar(140),
`query_route` varchar(140),
`search_term_param_name` varchar(140),
`response_result_key_path` varchar(140),
`post_route` varchar(140),
`post_route_key_list` varchar(140),
`post_title_key` varchar(140),
`post_description_key` varchar(140),
`source_doctype` varchar(140),
`result_title_field` varchar(140),
`result_preview_field` varchar(140),
`result_route_field` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:49,169 WARNING database DDL Query made to DB:
create table `tabHD Action` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`is_enabled` int(1) not null default 0,
`button_label` varchar(140),
`button_icon` varchar(140),
`is_external_link` int(1) not null default 0,
`action` longtext,
`cond_hidden` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:49,274 WARNING database DDL Query made to DB:
create table `tabHD Agent` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`agent_name` varchar(140),
`user_image` varchar(140),
`is_active` int(1) not null default 1,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:49,384 WARNING database DDL Query made to DB:
create table `tabHD Ticket Feedback Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`rating` decimal(3,2),
`label` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:07:49,502 WARNING database DDL Query made to DB:
create table `tabHD Desk Account Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`request_key` varchar(140),
`ip_address` varchar(140),
`email` varchar(140) unique,
`user` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-06-04 14:08:05,879 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-04 16:24:07,146 WARNING database DDL Query made to DB:
ALTER TABLE `tabHD Ticket` MODIFY `resolution_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `feedback_rating` decimal(3,2), MODIFY `total_hold_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9)
2025-06-04 16:24:40,031 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-06-04 16:24:41,162 WARNING database DDL Query made to DB:
ALTER TABLE `tabHD Ticket` MODIFY `resolution_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `avg_response_time` decimal(21,9), MODIFY `feedback_rating` decimal(3,2), MODIFY `total_hold_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9)
2025-06-04 16:37:58,014 WARNING database DDL Query made to DB:
ALTER TABLE `tabHD Ticket` MODIFY `feedback_rating` decimal(3,2), MODIFY `avg_response_time` decimal(21,9), MODIFY `user_resolution_time` decimal(21,9), MODIFY `first_response_time` decimal(21,9), MODIFY `resolution_time` decimal(21,9), MODIFY `total_hold_time` decimal(21,9)
